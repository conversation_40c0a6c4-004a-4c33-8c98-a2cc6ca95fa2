import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import type { UploadFile } from 'element-plus'
import { useMediaMainStore } from '@/stores/media-main'
import type { ProcessingOptions } from '@/stores/media-tasks'

export interface MediaProcessorConfig {
  taskType: 'video-convert' | 'audio-extract' | 'asr' | 'image-process'
  defaultOptions: ProcessingOptions
  maxFileSize?: number
  maxFileCount?: number
  supportedFormats?: string[]
}

/**
 * 通用媒体处理 Composable
 * 提供所有媒体处理组件的通用逻辑
 */
export function useMediaProcessor(config: MediaProcessorConfig) {
  const mediaStore = useMediaMainStore()

  // ==================== 响应式状态 ====================
  const fileList = ref<UploadFile[]>([])
  const outputDirectory = ref('')
  const isProcessing = ref(false)
  const filePathMap = ref<Map<string, string>>(new Map())
  const currentTasks = ref<any[]>([])
  const taskResults = ref<any[]>([])

  // 处理选项
  const processingOptions = reactive<ProcessingOptions>({
    ...config.defaultOptions
  })

  // ==================== 计算属性 ====================

  // 任务状态
  const taskStatus = computed(() => {
    if (isProcessing.value) return 'processing'
    if (currentTasks.value.some(t => t.status === 'completed')) return 'completed'
    if (currentTasks.value.some(t => t.status === 'error')) return 'error'
    return 'idle'
  })

  // 是否可以开始处理
  const canStartProcessing = computed(() => {
    return fileList.value.length > 0 &&
      outputDirectory.value &&
      !isProcessing.value
  })

  // 是否可以创建批量任务
  const canCreateBatch = computed(() => {
    return fileList.value.length > 1 &&
      outputDirectory.value &&
      !isProcessing.value
  })

  // 处理进度
  const overallProgress = computed(() => {
    if (currentTasks.value.length === 0) return 0
    const totalProgress = currentTasks.value.reduce((sum, task) => sum + task.progress, 0)
    return Math.floor(totalProgress / currentTasks.value.length)
  })

  // 成功的任务数
  const completedTasksCount = computed(() => {
    return currentTasks.value.filter(t => t.status === 'completed').length
  })

  // 失败的任务数
  const failedTasksCount = computed(() => {
    return currentTasks.value.filter(t => t.status === 'error').length
  })

  // ==================== 文件操作方法 ====================

  const handleFileAdded = (file: UploadFile, filePath: string) => {
    filePathMap.value.set(file.uid.toString(), filePath)

    // 如果没有设置输出目录，使用默认目录
    if (!outputDirectory.value) {
      outputDirectory.value = mediaStore.settingsStore.settings.defaultOutputDir
    }

    console.log(`[MediaProcessor] 文件已添加: ${file.name}`)
  }

  const handleFileRemoved = (file: UploadFile) => {
    filePathMap.value.delete(file.uid.toString())

    // 如果没有文件了，清理任务状态
    if (fileList.value.length === 0) {
      currentTasks.value = []
      taskResults.value = []
      isProcessing.value = false
    }

    console.log(`[MediaProcessor] 文件已移除: ${file.name}`)
  }

  const handleError = (message: string) => {
    ElMessage.error(message)
    console.error(`[MediaProcessor] 错误: ${message}`)
  }

  // ==================== 任务操作方法 ====================

  const startSingleTask = async (fileIndex?: number) => {
    if (!canStartProcessing.value) {
      ElMessage.warning('请检查文件和输出目录设置')
      return
    }

    const targetFile = fileIndex !== undefined ? fileList.value[fileIndex] : fileList.value[0]
    if (!targetFile) {
      ElMessage.error('未找到要处理的文件')
      return
    }

    const filePath = filePathMap.value.get(targetFile.uid.toString())
    if (!filePath) {
      ElMessage.error('文件路径无效')
      return
    }

    try {
      isProcessing.value = true

      // 生成输出文件名
      const outputFileName = mediaStore.tasksStore.generateOutputFileName(
        targetFile.name,
        config.taskType,
        processingOptions
      )
      const outputPath = `${outputDirectory.value}/${outputFileName}`

      // 创建单文件任务
      const taskId = await mediaStore.tasksStore.createSingleTask({
        type: config.taskType,
        fileName: targetFile.name,
        filePath,
        outputPath,
        options: JSON.parse(JSON.stringify(processingOptions)),
        fileSize: targetFile.size // 添加文件大小
      })

      // 开始任务
      await mediaStore.startSingleTask(taskId)

      ElMessage.success('任务已开始处理')

    } catch (error: any) {
      ElMessage.error(`开始任务失败: ${error.message}`)
      isProcessing.value = false
    }
  }

  const createBatchTask = async () => {
    if (!canCreateBatch.value) {
      ElMessage.warning('至少需要 2 个文件才能创建批量任务')
      return
    }

    try {
      const files = fileList.value.map(file => ({
        fileName: file.name,
        filePath: filePathMap.value.get(file.uid.toString()) || file.name,
        fileSize: file.size // 添加文件大小
      }))

      const batchId = await mediaStore.tasksStore.createBatchTask({
        name: `${getTaskTypeName()}批量任务_${new Date().toLocaleString()}`,
        type: config.taskType,
        files,
        outputDirectory: outputDirectory.value,
        options: JSON.parse(JSON.stringify(processingOptions))
      })

      ElMessage.success('批量任务已创建')
      clearAll()

    } catch (error: any) {
      ElMessage.error(`创建批量任务失败: ${error.message}`)
    }
  }

  const retryTask = async (task: any) => {
    try {
      await mediaStore.retrySingleTask(task.id)

      // 更新本地任务状态
      const localTask = currentTasks.value.find(t => t.id === task.id)
      if (localTask) {
        localTask.status = 'pending'
        localTask.progress = 0
        localTask.error = undefined
      }

      ElMessage.success('任务重试已开始')
    } catch (error: any) {
      ElMessage.error(`重试失败: ${error.message}`)
    }
  }

  const pauseTask = async (task: any) => {
    try {
      await mediaStore.pauseSingleTask(task.id)

      // 更新本地任务状态
      const localTask = currentTasks.value.find(t => t.id === task.id)
      if (localTask) {
        localTask.status = 'paused'
      }

      ElMessage.success('任务已暂停')
    } catch (error: any) {
      ElMessage.error(`暂停失败: ${error.message}`)
    }
  }

  const removeTask = async (task: any) => {
    try {
      await mediaStore.tasksStore.removeSingleTask(task.id)

      // 从本地任务列表中移除
      const index = currentTasks.value.findIndex(t => t.id === task.id)
      if (index > -1) {
        currentTasks.value.splice(index, 1)
      }

      ElMessage.success('任务已删除')
    } catch (error: any) {
      ElMessage.error(`删除失败: ${error.message}`)
    }
  }

  // ==================== 选项处理方法 ====================

  const handleDirectorySelected = (directory: string) => {
    outputDirectory.value = directory
    console.log(`[MediaProcessor] 输出目录已设置: ${directory}`)
  }

  const handlePresetSaved = (presetName: string) => {
    ElMessage.success(`预设 "${presetName}" 保存成功`)
  }

  const updateProcessingOptions = (newOptions: Partial<ProcessingOptions>) => {
    Object.assign(processingOptions, newOptions)
  }

  // ==================== 工具方法 ====================

  const clearAll = () => {
    fileList.value = []
    currentTasks.value = []
    taskResults.value = []
    isProcessing.value = false
    filePathMap.value.clear()
    console.log('[MediaProcessor] 已清空所有数据')
  }

  const getTaskTypeName = (): string => {
    const names = {
      'video-convert': '视频转换',
      'audio-extract': '音频提取',
      'asr': '语音识别',
      'image-process': '图片处理'
    }
    return names[config.taskType] || config.taskType
  }

  // ==================== 生命周期 ====================

  onMounted(async () => {
    if (!mediaStore.isInitialized) {
      await mediaStore.initialize()
    }
  })

  // ==================== 返回接口 ====================

  return {
    // 响应式状态
    fileList,
    outputDirectory,
    isProcessing,
    filePathMap,
    currentTasks,
    taskResults,
    processingOptions,

    // 计算属性
    taskStatus,
    canStartProcessing,
    canCreateBatch,
    overallProgress,
    completedTasksCount,
    failedTasksCount,

    // 文件操作方法
    handleFileAdded,
    handleFileRemoved,
    handleError,

    // 任务操作方法
    startSingleTask,
    createBatchTask,
    retryTask,
    pauseTask,
    removeTask,

    // 选项处理方法
    handleDirectorySelected,
    handlePresetSaved,
    updateProcessingOptions,

    // 工具方法
    clearAll,
    getTaskTypeName,

    // Store 引用
    mediaStore
  }
}