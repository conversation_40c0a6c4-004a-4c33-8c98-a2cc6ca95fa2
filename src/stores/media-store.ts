import { defineStore } from 'pinia'
import { useMediaMainStore } from './media-main'

// 重新导出类型定义（保持向后兼容）
export type {
  BaseTask,
  SingleTask,
  BatchSubTask,
  BatchTask,
  MediaTask,
  ProcessingOptions,
  TaskResult
} from './media-tasks'

export type { ProcessingStats } from './media-stats'
export type { MediaSettings } from './media-settings'

/**
 * 媒体 Store（统一入口）
 * 委托给新的模块化架构，保持向后兼容
 */
export const useMediaStore = defineStore('media', () => {
  // 使用主 store
  const mainStore = useMediaMainStore()

  // 初始化主 store
  const initialize = async () => {
    await mainStore.initialize()
  }

  // 委托所有属性和方法给主 store（保持 API 兼容性）
  return {
    // 子 stores
    tasksStore: mainStore.tasksStore,
    settingsStore: mainStore.settingsStore,
    statsStore: mainStore.statsStore,

    // 核心状态
    singleTasks: mainStore.tasksStore.singleTasks,
    batchTasks: mainStore.tasksStore.batchTasks,
    taskResults: mainStore.tasksStore.taskResults,
    stats: mainStore.statsStore.stats,
    settings: mainStore.settingsStore.settings,
    isInitialized: mainStore.isInitialized,
    activeTasksCount: mainStore.tasksStore.activeTasksCount,

    // 计算属性
    allSingleTasks: mainStore.allTasks,
    allBatchTasks: mainStore.allBatchTasks,
    allTasks: mainStore.allTasks,
    activeTasks: mainStore.activeTasks,
    completedTasks: mainStore.tasksStore.completedTasks,
    failedTasks: mainStore.tasksStore.failedTasks,
    overallProgress: mainStore.tasksStore.overallProgress,
    allResults: mainStore.allResults,
    successfulResults: mainStore.tasksStore.successfulResults,
    failedResults: mainStore.tasksStore.failedResults,
    activeSingleTasks: mainStore.tasksStore.activeSingleTasks,
    activeBatchTasks: mainStore.tasksStore.activeBatchTasks,
    completedSingleTasks: mainStore.tasksStore.completedSingleTasks,
    failedSingleTasks: mainStore.tasksStore.failedSingleTasks,

    // 初始化和设置
    initialize,
    loadSettings: mainStore.settingsStore.loadSettings,
    saveSettings: mainStore.settingsStore.saveSettings,

    // 单文件任务管理
    createSingleTask: mainStore.tasksStore.createSingleTask,
    startSingleTask: mainStore.startSingleTask,
    pauseSingleTask: mainStore.pauseSingleTask,
    retrySingleTask: mainStore.retrySingleTask,
    removeSingleTask: mainStore.tasksStore.removeSingleTask,

    // 批量任务管理
    createBatchTask: mainStore.tasksStore.createBatchTask,
    startBatchTask: async (_batchId: string, _options?: { concurrency?: number }) => {
      // 批量任务逻辑需要在主 store 中实现
      console.warn('[MediaStore] 批量任务处理需要在主 store 中实现')
    },
    pauseBatchTask: async (_batchId: string) => {
      console.warn('[MediaStore] 批量任务暂停需要在主 store 中实现')
    },
    retryBatchFailedTasks: async (_batchId: string) => {
      console.warn('[MediaStore] 批量任务重试需要在主 store 中实现')
    },
    removeBatchTask: mainStore.tasksStore.removeBatchTask,
    removeBatchSubTask: async (_batchId: string, __taskId: string) => {
      console.warn('[MediaStore] 批量子任务删除需要在主 store 中实现')
    },

    // 全局操作
    pauseAllTasks: mainStore.pauseAllTasks,
    clearAllTasks: mainStore.tasksStore.clearAllTasks,
    refreshStats: mainStore.refreshStats,
    clearResults: mainStore.tasksStore.clearResults,
    cleanupCompletedTasks: mainStore.cleanupCompletedTasks,
    clearStats: mainStore.statsStore.clearStats,

    // 工具方法
    generateTaskId: mainStore.tasksStore.generateTaskId,
    generateBatchId: mainStore.tasksStore.generateBatchId,
    generateOutputFileName: mainStore.tasksStore.generateOutputFileName
  }
})