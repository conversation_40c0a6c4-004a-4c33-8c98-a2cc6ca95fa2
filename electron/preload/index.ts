import { contextBridge, ipc<PERSON>enderer, webUtils } from 'electron'

// --------- Expose some API to the Renderer process ---------
contextBridge.exposeInMainWorld('ipcRenderer', {
    on(...args: Parameters<typeof ipcRenderer.on>) {
        const [channel, listener] = args
        return ipcRenderer.on(channel, (event, ...args) => listener(event, ...args))
    },
    off(...args: Parameters<typeof ipcRenderer.off>) {
        const [channel, ...omit] = args
        return ipcRenderer.off(channel, ...omit)
    },
    send(...args: Parameters<typeof ipcRenderer.send>) {
        const [channel, ...omit] = args
        return ipcRenderer.send(channel, ...omit)
    },
    invoke(...args: Parameters<typeof ipcRenderer.invoke>) {
        const [channel, ...omit] = args
        return ipcRenderer.invoke(channel, ...omit)
    },

    // You can expose other APTs you need here.
    // ...
})

// --------- Preload scripts can use Node.js API (except Electron APIs) ---------
// If you need to use Node.js modules in the preload script, make sure they are
// included in your dependencies and not devDependencies.
// Example:
// import fs from 'node:fs'
// contextBridge.exposeInMainWorld('fs', fs)


// --------- Expose a custom API for your app ---------
// This is where we will add our specific API functions later
contextBridge.exposeInMainWorld('electronAPI', {
    // General IPC invoke/send (optional, but can be useful)
    // invoke: (channel: string, ...args: any[]) => ipcRenderer.invoke(channel, ...args),
    // send: (channel: string, ...args: any[]) => ipcRenderer.send(channel, ...args),
    // on: (channel: string, listener: (...args: any[]) => void) => {
    //     ipcRenderer.on(channel, listener);
    //     return () => { // Return an unsubscribe function
    //         ipcRenderer.removeListener(channel, listener);
    //     };
    // },

    // Structured API based on modules
    app: {
        openDialog: (params: Electron.OpenDialogOptions) => ipcRenderer.invoke('open-dialog', params),
        showItemInFolder: (path: string) => ipcRenderer.invoke('show-item-in-folder', path),
        openExternal: (url: string) => ipcRenderer.invoke('open-external', url),
        clipboardWriteText: (text: string) => ipcRenderer.invoke('clipboard-write-text', text),
        getLoginInfo: () => ipcRenderer.invoke('get-login-info'),
        login: () => ipcRenderer.send('login'),
        logout: () => ipcRenderer.send('logout'),
        showLoginWindow: (url?: string) => ipcRenderer.send('toggle-login-window', 'show', url), // This handler was commented out in main, but keeping the preload definition for completeness
        onLoginStatusChanged: (callback: () => void) => {
            const listener = () => callback();
            ipcRenderer.on('login-status-changed', listener);
            return () => { ipcRenderer.removeListener('login-status-changed', listener); };
        },
        // The 'login-status-detected' event is handled internally in main process, not exposed to renderer directly
    },
    excel: {
        fileParse: (filePath: string) => ipcRenderer.invoke('excel-file-parse', filePath),
        fileBuild: (obj: { path: string, data: any }) => ipcRenderer.invoke('excel-file-build', obj),
        exportToExcel: (data: any, filePath: string) => ipcRenderer.invoke('export-to-excel', { data, path: filePath }), // Corrected channel name based on main process
    },
    fileSystem: {
        pathDirname: (filePath: string) => ipcRenderer.invoke('path-dirname', filePath),
        pathBasename: (filePath: string) => ipcRenderer.invoke('path-basename', filePath),
        pathSep: () => ipcRenderer.invoke('path-sep'),
        mkdirSync: (dirPath: string, options?: any) => ipcRenderer.invoke('fs-mkdirSync', dirPath, options),
        existsSync: (filePath: string) => ipcRenderer.invoke('fs-existsSync', filePath),
    },
    updater: {
        checkForUpdates: (isManualCheck = false) => ipcRenderer.send('check-for-updates', isManualCheck),
        downloadUpdate: (info: any) => ipcRenderer.send('download-update', info),
        quitAndInstall: (filePath: string) => ipcRenderer.send('quit-and-install', filePath),

        // Updater Events
        onCheckingForUpdate: (callback: () => void) => {
            const listener = () => callback();
            ipcRenderer.on('checking-for-update', listener);
            return () => { ipcRenderer.removeListener('checking-for-update', listener); };
        },
        onUpdateAvailable: (callback: (info: any, isManual: boolean) => void) => {
            const listener = (event: Electron.IpcRendererEvent, info: any, isManual: boolean) => callback(info, isManual);
            ipcRenderer.on('update-available', listener);
            return () => { ipcRenderer.removeListener('update-available', listener); };
        },
        onUpdateNotAvailable: (callback: (isManual: boolean) => void) => {
            const listener = (event: Electron.IpcRendererEvent, isManual: boolean) => callback(isManual);
            ipcRenderer.on('update-not-available', listener);
            return () => { ipcRenderer.removeListener('update-not-available', listener); };
        },
        onUpdateError: (callback: (error: string, isManual: boolean) => void) => {
            const listener = (event: Electron.IpcRendererEvent, error: string, isManual: boolean) => callback(error, isManual);
            ipcRenderer.on('update-error', listener);
            return () => { ipcRenderer.removeListener('update-error', listener); };
        },
        onDownloadProgress: (callback: (progressObj: { progress: number; receivedBytes: number; totalBytes: number }) => void) => {
            const listener = (event: Electron.IpcRendererEvent, progressObj: any) => callback(progressObj);
            ipcRenderer.on('download-progress', listener);
            return () => { ipcRenderer.removeListener('download-progress', listener); };
        },
        onDownloadCompleted: (callback: (filePath: string) => void) => {
            const listener = (event: Electron.IpcRendererEvent, filePath: string) => callback(filePath);
            ipcRenderer.on('download-completed', listener);
            return () => { ipcRenderer.removeListener('download-completed', listener); };
        },
        onDownloadError: (callback: (error: string) => void) => {
            const listener = (event: Electron.IpcRendererEvent, error: string) => callback(error);
            ipcRenderer.on('download-error', listener);
            return () => { ipcRenderer.removeListener('download-error', listener); };
        },
        onInstallError: (callback: (error: string) => void) => {
            const listener = (event: Electron.IpcRendererEvent, error: string) => callback(error);
            ipcRenderer.on('install-error', listener);
            return () => { ipcRenderer.removeListener('install-error', listener); };
        },
    },
    xhs: {
        noteDetail: (input: string) => ipcRenderer.invoke('xhs-note-detail', input),
        noteImgs: (item: { text: string }) => ipcRenderer.invoke('xhs-note-imgs', item),
        noteImgsSave: (item: { dir: string, imgs: any[], desc: string, title: string }) => ipcRenderer.invoke('xhs-note-imgs-save', item),
        noteVideoSave: (item: { dir: string, video: any, title: string, desc: string }) => ipcRenderer.invoke('xhs-note-video-save', item),
        notesSave: (item: { dir: string, notes: any[] }) => ipcRenderer.invoke('xhs-notes-save', item),
        notesFetch: (item: { keyword: string, page: number, type: string }) => ipcRenderer.invoke('xhs-notes-fetch', item),
        noteCheckWord: (text: string) => ipcRenderer.invoke('xhs-note-check-word', text),
        noteCheckUser: () => ipcRenderer.invoke('xhs-note-check-user'),
        userInfo: (userId: string) => ipcRenderer.invoke('xhs-user-info', userId),
        noteCheck: (noteId: string, redId: string) => ipcRenderer.invoke('xhs-note-check', { nodeId: noteId, redId: redId }), // Corrected channel name and parameter structure based on main process
    },
    media: {
        // 文件路径获取（使用webUtils）
        getPathForFile: (file: File) => webUtils.getPathForFile(file),

        // 音视频转换
        convertVideo: (inputPath: string, outputPath: string, options: any) => ipcRenderer.invoke('media-convert-video', inputPath, outputPath, options),
        extractAudio: (videoPath: string, audioPath: string, options?: any) => ipcRenderer.invoke('media-extract-audio', videoPath, audioPath, options),

        // 语音识别
        extractText: (audioPath: string, options: any) => ipcRenderer.invoke('media-extract-text', audioPath, options),

        // 图片处理
        processImages: (imagePaths: string[], options: any) => ipcRenderer.invoke('media-process-images', imagePaths, options),

        // 批量任务管理
        createBatchTask: (tasks: any[]) => ipcRenderer.invoke('media-create-batch-task', tasks),
        startBatchTask: (batchId: string) => ipcRenderer.invoke('media-start-batch-task', batchId),
        getBatchProgress: (batchId: string) => ipcRenderer.invoke('media-get-batch-progress', batchId),
        pauseBatchTask: (batchId: string) => ipcRenderer.invoke('media-pause-batch-task', batchId),
        cancelBatchTask: (batchId: string) => ipcRenderer.invoke('media-cancel-batch-task', batchId),

        // 单个任务控制
        retryTask: (batchId: string, taskId: string) => ipcRenderer.invoke('media-retry-task', batchId, taskId),
        getTaskStatus: (taskId: string) => ipcRenderer.invoke('media-get-task-status', taskId),

        // 任务控制
        pauseTask: (taskId: string) => ipcRenderer.invoke('media-pause-task', taskId),
        resumeTask: (taskId: string) => ipcRenderer.invoke('media-resume-task', taskId),
        cancelTask: (taskId: string) => ipcRenderer.invoke('media-cancel-task', taskId),

        // FFmpeg状态检查
        checkFFmpegStatus: () => ipcRenderer.invoke('media-check-ffmpeg-status'),
        getActiveTasks: () => ipcRenderer.invoke('media-get-active-tasks'),

        // 进度监听
        onTaskProgress: (callback: (data: { taskId: string; progress: number }) => void) => {
            const listener = (event: Electron.IpcRendererEvent, data: { taskId: string; progress: number }) => callback(data);
            ipcRenderer.on('media-task-progress', listener);
            return () => { ipcRenderer.removeListener('media-task-progress', listener); };
        },

        // 工具方法
        getSupportedFormats: () => ipcRenderer.invoke('media-get-supported-formats'),
        getFileInfo: (filePath: string) => ipcRenderer.invoke('media-get-file-info', filePath),
        cleanupTemp: () => ipcRenderer.invoke('media-cleanup-temp'),
        getStats: () => ipcRenderer.invoke('media-get-stats'),
        updateStats: () => ipcRenderer.invoke('media-update-stats'),
        persistStats: (stats: any) => ipcRenderer.invoke('media-persist-stats', stats),
        clearStats: () => ipcRenderer.invoke('media-clear-stats'),
        notifyStatsUpdate: () => ipcRenderer.invoke('media-notify-stats-update'),

        // 设置管理
        getSettings: () => ipcRenderer.invoke('media-get-settings'),
        saveSettings: (settings: any) => ipcRenderer.invoke('media-save-settings', settings),

        // 任务持久化
        getUncompletedTasks: () => ipcRenderer.invoke('media-get-uncompleted-tasks'),
        persistTask: (task: any) => ipcRenderer.invoke('media-persist-task', task),
        persistBatch: (batch: any) => ipcRenderer.invoke('media-persist-batch', batch),
        deleteTask: (taskId: string) => ipcRenderer.invoke('media-delete-task', taskId),
        deleteBatch: (batchId: string) => ipcRenderer.invoke('media-delete-batch', batchId),
        clearAllTasks: () => ipcRenderer.invoke('media-clear-all-tasks'),

        // 任务结果管理
        getTaskResults: () => ipcRenderer.invoke('media-get-task-results'),
        persistTaskResult: (result: any) => ipcRenderer.invoke('media-persist-task-result', result),
        deleteTaskResult: (resultId: string) => ipcRenderer.invoke('media-delete-task-result', resultId),
        clearTaskResults: () => ipcRenderer.invoke('media-clear-task-results'),
    }
})

console.log('Preload script loaded.')