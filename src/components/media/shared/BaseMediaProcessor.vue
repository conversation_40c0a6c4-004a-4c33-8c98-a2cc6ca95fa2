<template>
  <div class="app-card base-media-processor" :class="componentClass">
    <!-- 标题栏 -->
    <div class="section-header">
      <h3>
        <Icon :icon="icon" class="section-icon" />
        {{ title }}
      </h3>
      <el-button 
        size="small" 
        @click="clearAll" 
        v-if="fileList.length > 0"
      >
        <Icon icon="mdi:trash-can-outline" />
        清空
      </el-button>
    </div>

    <!-- 文件上传器 -->
    <slot name="uploader" v-bind="uploaderProps">
      <FileUploader
        v-model="fileList"
        :task-type="taskType"
        :max-file-size="maxFileSize"
        :max-file-count="maxFileCount"
        :upload-text="uploadText"
        :hint="uploadHint"
        @file-added="handleFileAdded"
        @file-removed="handleFileRemoved"
        @error="handleError"
      />
    </slot>

    <!-- 自定义内容区域（在文件上传和处理选项之间） -->
    <slot name="custom-content" v-if="fileList.length > 0" />

    <!-- 处理选项 -->
    <div class="options-section" v-if="fileList.length > 0 && showOptions">
      <el-divider>{{ optionsTitle }}</el-divider>
      <slot name="options" v-bind="optionsProps">
        <ProcessingOptions
          v-model="processingOptions"
          :task-type="taskType"
          :show-presets="showPresets"
          :show-save-preset="showSavePreset"
          @preset-saved="handlePresetSaved"
        />
      </slot>
    </div>

    <!-- 输出目录选择器 -->
    <slot name="output-directory" v-bind="outputDirectoryProps" v-if="fileList.length > 0 && showOutputDirectory">
      <OutputDirectorySelector
        v-model="outputDirectory"
        :label="outputDirectoryLabel"
        @directory-selected="handleDirectorySelected"
        @error="handleError"
      />
    </slot>

    <!-- 任务控制 -->
    <slot name="task-controls" v-bind="taskControlsProps" v-if="fileList.length > 0 && showTaskControls">
      <TaskControls
        mode="single"
        :status="taskStatus"
        :is-processing="isProcessing"
        :can-start="canStartProcessing"
        :show-batch-create="canCreateBatch"
        @start="startSingleTask()"
        @create-batch="createBatchTask"
        @pause="() => {}"
        @retry="() => {}"
      />
    </slot>

    <!-- 进度显示 -->
    <slot name="progress" v-bind="progressProps" v-if="isProcessing">
      <TaskProgress
        :progress="overallProgress"
        :status="taskStatus"
        :completed-count="completedTasksCount"
        :failed-count="failedTasksCount"
        :total-count="currentTasks.length"
      />
    </slot>

    <!-- 批量操作区域 -->
    <div class="batch-section" v-if="fileList.length > 1 && showBatchSection">
      <el-divider>批量操作</el-divider>
      <div class="batch-actions">
        <slot name="batch-actions" v-bind="batchActionsProps">
          <el-button 
            type="primary" 
            @click="createBatchTask"
            :disabled="!canCreateBatch"
          >
            <Icon icon="mdi:play-box-multiple" />
            创建批量任务
          </el-button>
        </slot>
      </div>
    </div>

    <!-- 结果面板 -->
    <slot name="results" v-bind="resultsProps" v-if="taskResults.length > 0">
      <TaskResultPanel
        :results="taskResults"
        :task-type="taskType"
        @retry="retryTask"
        @remove="removeTask"
      />
    </slot>

    <!-- 自定义底部内容 -->
    <slot name="footer" />
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { Icon } from '@iconify/vue'
import FileUploader from './FileUploader.vue'
import ProcessingOptions from './ProcessingOptions.vue'
import OutputDirectorySelector from './OutputDirectorySelector.vue'
import TaskControls from './TaskControls.vue'
import TaskProgress from './TaskProgress.vue'
import TaskResultPanel from './TaskResultPanel.vue'
import { useMediaProcessor, type MediaProcessorConfig } from './useMediaProcessor'

export interface BaseMediaProcessorProps {
  // 基础配置
  title: string
  icon: string
  componentClass?: string
  taskType: 'video-convert' | 'audio-extract' | 'asr' | 'image-process'
  
  // 文件上传配置
  maxFileSize?: number
  maxFileCount?: number
  uploadText?: string
  uploadHint?: string
  
  // 处理选项配置
  defaultOptions: any
  showOptions?: boolean
  optionsTitle?: string
  showPresets?: boolean
  showSavePreset?: boolean
  
  // 输出目录配置
  showOutputDirectory?: boolean
  outputDirectoryLabel?: string
  
  // 控制显示的区域
  showTaskControls?: boolean
  showBatchSection?: boolean
}

const props = withDefaults(defineProps<BaseMediaProcessorProps>(), {
  componentClass: '',
  maxFileSize: 100,
  maxFileCount: 10,
  uploadText: '拖拽文件到此处或点击上传',
  uploadHint: '请选择支持的文件格式',
  showOptions: true,
  optionsTitle: '处理设置',
  showPresets: true,
  showSavePreset: false,
  showOutputDirectory: true,
  outputDirectoryLabel: '输出目录',
  showTaskControls: true,
  showBatchSection: true
})

const emit = defineEmits<{
  fileUploaded: [payload: { name: string; size?: number; path: string }]
  taskStarted: [taskId: string]
  taskCompleted: [result: any]
  error: [message: string]
}>()

// 使用通用的媒体处理逻辑
const config: MediaProcessorConfig = {
  taskType: props.taskType,
  defaultOptions: props.defaultOptions,
  maxFileSize: props.maxFileSize,
  maxFileCount: props.maxFileCount
}

const {
  // 响应式状态
  fileList,
  outputDirectory,
  isProcessing,
  currentTasks,
  taskResults,
  processingOptions,

  // 计算属性
  taskStatus,
  canStartProcessing,
  canCreateBatch,
  overallProgress,
  completedTasksCount,
  failedTasksCount,

  // 方法
  handleFileAdded,
  handleFileRemoved,
  handleError,
  startSingleTask,
  createBatchTask,
  retryTask,
  removeTask,
  handleDirectorySelected,
  handlePresetSaved,
  clearAll
} = useMediaProcessor(config)

// 为插槽提供的 props 对象
const uploaderProps = computed(() => ({
  taskType: props.taskType,
  maxFileSize: props.maxFileSize,
  maxFileCount: props.maxFileCount,
  uploadText: props.uploadText,
  hint: props.uploadHint,
  fileList: fileList.value,
  onFileAdded: handleFileAdded,
  onFileRemoved: handleFileRemoved,
  onError: handleError
}))

const optionsProps = computed(() => ({
  taskType: props.taskType,
  modelValue: processingOptions,
  showPresets: props.showPresets,
  showSavePreset: props.showSavePreset,
  onPresetSaved: handlePresetSaved
}))

const outputDirectoryProps = computed(() => ({
  modelValue: outputDirectory.value,
  label: props.outputDirectoryLabel,
  onDirectorySelected: handleDirectorySelected,
  onError: handleError
}))

const taskControlsProps = computed(() => ({
  mode: 'single',
  status: taskStatus.value,
  isProcessing: isProcessing.value,
  canStart: canStartProcessing.value,
  showBatchCreate: canCreateBatch.value,
  onStart: () => startSingleTask(),
  onCreateBatch: createBatchTask
}))

const progressProps = computed(() => ({
  progress: overallProgress.value,
  status: taskStatus.value,
  completedCount: completedTasksCount.value,
  failedCount: failedTasksCount.value,
  totalCount: currentTasks.value.length
}))

const batchActionsProps = computed(() => ({
  canCreateBatch: canCreateBatch.value,
  onCreateBatch: createBatchTask
}))

const resultsProps = computed(() => ({
  results: taskResults.value,
  taskType: props.taskType,
  onRetry: retryTask,
  onRemove: removeTask
}))

// 扩展文件添加事件
const enhancedHandleFileAdded = (file: any, filePath: string) => {
  handleFileAdded(file, filePath)
  emit('fileUploaded', {
    name: file.name,
    size: file.size,
    path: filePath
  })
}

// 扩展错误处理
const enhancedHandleError = (message: string) => {
  handleError(message)
  emit('error', message)
}

// 重新绑定增强的事件处理器
const fileList_enhanced = computed({
  get: () => fileList.value,
  set: (value) => fileList.value = value
})

// 暴露给父组件的方法和状态
defineExpose({
  fileList,
  outputDirectory,
  isProcessing,
  taskStatus,
  canStartProcessing,
  canCreateBatch,
  startSingleTask,
  createBatchTask,
  clearAll,
  retryTask,
  removeTask
})
</script>

<style lang="scss" scoped>
.base-media-processor {
  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: $spacing-base;

    h3 {
      display: flex;
      align-items: center;
      gap: 8px;
      margin: 0;
      color: $text-primary;
      font-size: 16px;
      font-weight: 500;

      .section-icon {
        font-size: 18px;
      }
    }
  }

  .options-section {
    margin-top: $spacing-base;
  }

  .batch-section {
    margin-top: $spacing-base;
    
    .batch-actions {
      display: flex;
      gap: $spacing-base;
      justify-content: center;
      flex-wrap: wrap;
    }
  }

  // 不同任务类型的主题色
  &.video-converter .section-icon {
    color: $color-video;
  }

  &.audio-extractor .section-icon {
    color: $color-audio;
  }

  &.asr-processor .section-icon {
    color: $color-asr;
  }

  &.image-processor .section-icon {
    color: $color-image;
  }
}
</style>