import { ipc<PERSON>ain, dialog, shell, BrowserWindow, app, clipboard } from 'electron';
import * as fs from 'fs';
import * as path from 'path';
import Store from 'electron-store';

// Import modular handlers
import { registerXhsHandlers } from './ipc/xhs';
import { registerExcelHandlers } from './ipc/excel';
import { registerFileSystemHandlers } from './ipc/fileSystem';
import { registerUpdaterHandlers } from './ipc/updater';
import { registerAppHandlers } from './ipc/app';
import { registerMonitorIpcHandlers } from './ipc/monitor';
import { registerMediaHandlers, setupProgressEvents } from './ipc/media';

// Import services and utilities needed by handlers
import { XhsService } from './services/xhs-worker';
import { ExcelService } from './services/excel-worker';
import { MediaService } from './services/media-worker';
import { handleToggleLoginWindow, handleLogout, getLoginWindow, getMainWindow } from './windows';
import { startUpdateCheck, startDownload, quitAndInstall, openAndInstall } from './updater';
import { downloadAndConvert, downloadFile, getImgUrlByUrl } from './utils/img-utils';
import { MonitorService } from './services/monitor-service';


export function registerIpcHandlers(): void {
    const store = new Store();
    const xhsService = new XhsService();
    const excelService = new ExcelService();
    const mediaService = new MediaService();


    // Register modular handlers
    registerXhsHandlers(ipcMain, xhsService, fs, path, downloadAndConvert, downloadFile, getImgUrlByUrl);
    registerExcelHandlers(ipcMain, excelService);
    registerFileSystemHandlers(ipcMain, fs, path);
    registerUpdaterHandlers(ipcMain, startUpdateCheck, startDownload, quitAndInstall, openAndInstall);
    registerAppHandlers(ipcMain, store, dialog, shell, BrowserWindow, app, clipboard, handleToggleLoginWindow, handleLogout, getLoginWindow, getMainWindow);
    registerMediaHandlers(ipcMain, mediaService);
    setupProgressEvents(mediaService);

    // 延迟10秒后注册monitor的ipc handlers
    setTimeout(() => {
        registerMonitorIpcHandlers(ipcMain, new MonitorService());
    }, 10000);

    // Keep any handlers that don't fit neatly into modules or require specific context
    // For example, the 'login' handler might need access to xhsService and window management
    ipcMain.on("login", async (event) => {
        console.log(`[IPC] 用户尝试登录`);
        await xhsService.start();
    });

    // The 'logout' and 'login-status-detected' handlers are now in app.ts,
    // but if they had dependencies specific to this file, they could remain here.
    // Based on the current content, they fit well in app.ts.
}