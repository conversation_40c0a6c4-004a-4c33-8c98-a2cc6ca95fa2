// 媒体处理组件通用样式

// ==================== 主题颜色定义 ====================
$color-video: #409eff;      // 视频转换主题色
$color-audio: #67c23a;      // 音频提取主题色  
$color-asr: #e6a23c;        // 语音识别主题色
$color-image: #f56c6c;      // 图片处理主题色

// ==================== 通用样式 Mixin ====================

// 媒体处理器布局
@mixin media-processor-layout {
  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: $spacing-base;

    h3 {
      display: flex;
      align-items: center;
      gap: 8px;
      margin: 0;
      color: $text-primary;
      font-size: 16px;
      font-weight: 500;

      .section-icon {
        font-size: 18px;
      }
    }

    .el-button {
      margin-left: auto;
    }
  }

  .options-section,
  .settings-section {
    margin-top: $spacing-base;
    
    .el-divider {
      margin: $spacing-base 0;
    }
  }

  .batch-section {
    margin-top: $spacing-base;
    
    .batch-actions {
      display: flex;
      gap: $spacing-base;
      justify-content: center;
      flex-wrap: wrap;
      margin-top: $spacing-sm;
    }
  }

  .progress-section {
    margin-top: $spacing-base;
  }

  .results-section {
    margin-top: $spacing-base;
  }
}

// 不同任务类型的主题样式
@mixin task-type-theme($color) {
  .section-header .section-icon {
    color: $color;
  }

  .el-button--primary {
    background-color: $color;
    border-color: $color;

    &:hover {
      background-color: mix(white, $color, 20%);
      border-color: mix(white, $color, 20%);
    }

    &:active {
      background-color: mix(black, $color, 10%);
      border-color: mix(black, $color, 10%);
    }
  }

  .el-progress .el-progress-bar__inner {
    background-color: $color;
  }

  .el-tag--primary {
    background-color: mix(white, $color, 90%);
    color: $color;
    border-color: mix(white, $color, 70%);
  }
}

// 文件列表样式
@mixin file-list-styles {
  .file-list {
    .file-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: $spacing-sm;
      margin-bottom: $spacing-xs;
      background: $bg-light;
      border: 1px solid $border-light;
      border-radius: $border-radius;
      transition: all 0.3s ease;

      &:hover {
        background: mix(white, $color-primary, 95%);
        border-color: $color-primary;
      }

      .file-info {
        display: flex;
        align-items: center;
        gap: $spacing-sm;
        flex: 1;

        .file-icon {
          font-size: 24px;
          color: $color-primary;
        }

        .file-details {
          .file-name {
            font-weight: 500;
            color: $text-primary;
            margin-bottom: 2px;
          }

          .file-size {
            font-size: 12px;
            color: $text-secondary;
          }
        }
      }

      .file-actions {
        display: flex;
        gap: $spacing-xs;
      }
    }
  }
}

// 任务状态指示器样式
@mixin task-status-styles {
  .task-status {
    &.status-pending {
      color: $color-warning;
    }

    &.status-processing {
      color: $color-primary;
    }

    &.status-completed {
      color: $color-success;
    }

    &.status-error {
      color: $color-danger;
    }

    &.status-paused {
      color: $color-info;
    }
  }
}

// 选项面板样式
@mixin options-panel-styles {
  .options-panel {
    .option-group {
      margin-bottom: $spacing-base;

      .group-title {
        font-weight: 500;
        color: $text-primary;
        margin-bottom: $spacing-sm;
        font-size: 14px;
      }

      .option-row {
        display: flex;
        align-items: center;
        gap: $spacing-base;
        margin-bottom: $spacing-sm;

        &:last-child {
          margin-bottom: 0;
        }

        .option-label {
          min-width: 80px;
          color: $text-secondary;
          font-size: 13px;
        }

        .option-control {
          flex: 1;
        }
      }
    }
  }
}

// 结果面板样式
@mixin results-panel-styles {
  .results-panel {
    .result-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: $spacing-base;
      margin-bottom: $spacing-sm;
      background: $bg-light;
      border: 1px solid $border-light;
      border-radius: $border-radius;
      transition: all 0.3s ease;

      &.success {
        border-left: 4px solid $color-success;
      }

      &.error {
        border-left: 4px solid $color-danger;
      }

      .result-info {
        flex: 1;

        .result-title {
          font-weight: 500;
          color: $text-primary;
          margin-bottom: 4px;
        }

        .result-details {
          font-size: 12px;
          color: $text-secondary;
        }
      }

      .result-actions {
        display: flex;
        gap: $spacing-xs;
      }
    }
  }
}

// ==================== 具体组件样式类 ====================

// 视频转换器
.video-converter {
  @include media-processor-layout;
  @include task-type-theme($color-video);
  @include file-list-styles;
  @include task-status-styles;
  @include options-panel-styles;
  @include results-panel-styles;
}

// 音频提取器
.audio-extractor {
  @include media-processor-layout;
  @include task-type-theme($color-audio);
  @include file-list-styles;
  @include task-status-styles;
  @include options-panel-styles;
  @include results-panel-styles;
}

// 语音识别器
.asr-processor {
  @include media-processor-layout;
  @include task-type-theme($color-asr);
  @include file-list-styles;
  @include task-status-styles;
  @include options-panel-styles;
  @include results-panel-styles;

  // ASR 特有的样式
  .video-info-panel {
    margin-top: $spacing-base;
    
    .info-content {
      .info-item {
        display: flex;
        align-items: center;
        gap: $spacing-xs;
        margin-bottom: $spacing-xs;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }

  .transcript-preview {
    background: $bg-light;
    border: 1px solid $border-light;
    border-radius: $border-radius;
    padding: $spacing-base;
    max-height: 200px;
    overflow-y: auto;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 13px;
    line-height: 1.5;
  }
}

// 图片处理器
.image-processor {
  @include media-processor-layout;
  @include task-type-theme($color-image);
  @include file-list-styles;
  @include task-status-styles;
  @include options-panel-styles;
  @include results-panel-styles;

  // 图片处理特有的样式
  .preview-section {
    margin-top: $spacing-base;

    .preview-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
      gap: $spacing-base;
      margin-top: $spacing-sm;

      .preview-item {
        text-align: center;

        .preview-image {
          width: 100%;
          height: 120px;
          object-fit: cover;
          border-radius: $border-radius;
          border: 2px solid $border-light;
          transition: border-color 0.3s ease;

          &:hover {
            border-color: $color-image;
          }
        }

        .preview-name {
          margin-top: $spacing-xs;
          font-size: 12px;
          color: $text-secondary;
          word-break: break-all;
        }
      }
    }
  }
}

// 批量处理器特有样式
.batch-processor {
  @include media-processor-layout;
  @include file-list-styles;
  @include task-status-styles;
  @include results-panel-styles;

  .batch-summary {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: $spacing-base;
    margin-bottom: $spacing-base;

    .summary-card {
      padding: $spacing-base;
      background: $bg-light;
      border: 1px solid $border-light;
      border-radius: $border-radius;
      text-align: center;

      .summary-value {
        font-size: 24px;
        font-weight: bold;
        color: $color-primary;
        margin-bottom: $spacing-xs;
      }

      .summary-label {
        font-size: 12px;
        color: $text-secondary;
      }
    }
  }

  .batch-controls {
    display: flex;
    gap: $spacing-base;
    justify-content: center;
    flex-wrap: wrap;
    margin-bottom: $spacing-base;
  }
}

// ==================== 响应式设计 ====================

@media (max-width: 768px) {
  .base-media-processor {
    .section-header {
      flex-direction: column;
      align-items: flex-start;
      gap: $spacing-sm;

      h3 {
        width: 100%;
      }
    }

    .batch-actions {
      flex-direction: column;
      align-items: stretch;
    }

    .preview-grid {
      grid-template-columns: repeat(auto-fill, minmax(120px, 1fr)) !important;
    }

    .batch-summary {
      grid-template-columns: 1fr !important;
    }
  }
}