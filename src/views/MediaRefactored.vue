<template>
  <div class="media-page">
    <!-- 页面头部 - 总览和统计 -->
    <div class="page-header">
      <div class="header-left">
        <h1 class="page-title">
          <Icon icon="mdi:multimedia" class="title-icon" />
          媒体处理工具箱
        </h1>
        <p class="page-description">音视频转换、语音识别、图片处理一站式解决方案</p>
      </div>
      
      <div class="header-right">
        <MediaStatsCard 
          :stats="displayStats"
          :show-details="false"
          compact
        />
      </div>
    </div>

    <!-- 功能模块标签页 -->
    <div class="app-card modules-container">
      <el-tabs 
        v-model="activeModule" 
        type="card"
        class="module-tabs"
        @tab-change="handleModuleChange"
      >
        <el-tab-pane 
          v-for="module in moduleConfigs" 
          :key="module.name"
          :label="module.label" 
          :name="module.name"
        >
          <template #label>
            <div class="tab-label">
              <Icon :icon="module.icon" class="tab-icon" />
              <span>{{ module.label }}</span>
              <el-badge 
                v-if="getModuleTaskCount(module.name) > 0"
                :value="getModuleTaskCount(module.name)" 
                class="task-badge"
              />
            </div>
          </template>
          
          <!-- 动态渲染模块内容 -->
          <component 
            :is="module.component" 
            v-bind="module.props"
            @file-uploaded="handleFileUploaded"
            @task-started="handleTaskStarted" 
            @task-completed="handleTaskCompleted"
            @error="handleError"
          />
        </el-tab-pane>
      </el-tabs>
    </div>

    <!-- 全局任务管理面板 -->
    <div class="task-management" v-if="showTaskManagement">
      <div class="app-card current-tasks" v-if="activeTasks.length > 0">
        <div class="section-header">
          <h3>
            <Icon icon="mdi:playlist-play" />
            当前任务 ({{ activeTasks.length }})
          </h3>
          <div class="header-actions">
            <el-button-group>
              <el-button size="small" @click="pauseAllTasks" :disabled="!hasProcessingTasks">
                <Icon icon="mdi:pause" />
                全部暂停
              </el-button>
              <el-button size="small" @click="resumeAllTasks" :disabled="!hasPausedTasks">
                <Icon icon="mdi:play" />
                全部恢复
              </el-button>
              <el-button size="small" type="danger" @click="clearAllTasks">
                <Icon icon="mdi:trash-can-outline" />
                清空全部
              </el-button>
            </el-button-group>
          </div>
        </div>

        <GlobalTaskList 
          :tasks="activeTasks"
          :allow-control="true"
          :group-by-type="true"
          @task-action="handleTaskAction"
        />
      </div>

      <!-- 处理结果面板 -->
      <div class="app-card results-panel" v-if="recentResults.length > 0">
        <div class="section-header">
          <h3>
            <Icon icon="mdi:check-circle" />
            最近结果 ({{ allResults.length }})
          </h3>
          <div class="header-actions">
            <el-button size="small" @click="showAllResults">
              <Icon icon="mdi:eye" />
              查看全部
            </el-button>
            <el-button size="small" @click="exportResults">
              <Icon icon="mdi:download" />
              导出结果
            </el-button>
            <el-button size="small" type="danger" @click="clearResults">
              <Icon icon="mdi:trash-can-outline" />
              清空结果
            </el-button>
          </div>
        </div>

        <GlobalResultList 
          :results="recentResults"
          :compact="true"
          @result-action="handleResultAction"
        />
      </div>
    </div>

    <!-- 详细统计面板（可折叠） -->
    <div class="app-card stats-panel" v-if="showDetailedStats">
      <el-collapse v-model="expandedStats">
        <el-collapse-item name="stats" title="详细统计信息">
          <ProcessingStatsPanel 
            :stats="stats"
            :show-charts="true"
            :show-breakdown="true"
            @refresh="refreshStats"
            @clear="clearStats"
          />
        </el-collapse-item>
      </el-collapse>
    </div>

    <!-- 浮动工具栏 -->
    <div class="floating-toolbar" v-if="showFloatingToolbar">
      <el-button-group direction="vertical">
        <el-tooltip content="刷新统计" placement="left">
          <el-button size="small" @click="refreshStats" :loading="isRefreshingStats">
            <Icon icon="mdi:refresh" />
          </el-button>
        </el-tooltip>
        
        <el-tooltip content="全局设置" placement="left">
          <el-button size="small" @click="showGlobalSettings">
            <Icon icon="mdi:cog" />
          </el-button>
        </el-tooltip>
        
        <el-tooltip content="帮助文档" placement="left">
          <el-button size="small" @click="showHelp">
            <Icon icon="mdi:help-circle" />
          </el-button>
        </el-tooltip>
        
        <el-tooltip :content="taskManagementToggleText" placement="left">
          <el-button size="small" @click="toggleTaskManagement">
            <Icon :icon="showTaskManagement ? 'mdi:eye-off' : 'mdi:eye'" />
          </el-button>
        </el-tooltip>
      </el-button-group>
    </div>

    <!-- 全局设置对话框 -->
    <MediaSettingsDialog 
      v-model="settingsVisible"
      @settings-saved="handleSettingsSaved"
    />

    <!-- 帮助对话框 -->
    <MediaHelpDialog v-model="helpVisible" />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Icon } from '@iconify/vue'
import { storeToRefs } from 'pinia'
import { useMediaMainStore } from '@/stores/media-main'

// 懒加载组件
import VideoConverterRefactored from '@/components/media/VideoConverterRefactored.vue'
import ASRProcessor from '@/components/media/ASRProcessor.vue'
import ImageProcessor from '@/components/media/ImageProcessor.vue'
import BatchProcessor from '@/components/media/BatchProcessor.vue'

// 工具组件
import MediaStatsCard from '@/components/media/shared/MediaStatsCard.vue'
import GlobalTaskList from '@/components/media/shared/GlobalTaskList.vue'
import GlobalResultList from '@/components/media/shared/GlobalResultList.vue'
import ProcessingStatsPanel from '@/components/media/shared/ProcessingStatsPanel.vue'
import MediaSettingsDialog from '@/components/media/shared/MediaSettingsDialog.vue'
import MediaHelpDialog from '@/components/media/shared/MediaHelpDialog.vue'

// Store
const mediaStore = useMediaMainStore()
const { 
  isInitialized,
  activeTasks,
  allResults,
  stats
} = storeToRefs(mediaStore)

// ==================== 响应式状态 ====================
const activeModule = ref('video')
const showTaskManagement = ref(true)
const showDetailedStats = ref(false)
const showFloatingToolbar = ref(true)
const expandedStats = ref<string[]>([])
const settingsVisible = ref(false)
const helpVisible = ref(false)
const isRefreshingStats = ref(false)

// ==================== 模块配置 ====================
const moduleConfigs = [
  {
    name: 'video',
    label: '音视频转换',
    icon: 'mdi:video-outline',
    component: VideoConverterRefactored,
    props: {}
  },
  {
    name: 'asr',
    label: '语音识别',
    icon: 'mdi:microphone',
    component: ASRProcessor,
    props: {}
  },
  {
    name: 'image',
    label: '图片处理',
    icon: 'mdi:image-outline',
    component: ImageProcessor,
    props: {}
  },
  {
    name: 'batch',
    label: '批量处理',
    icon: 'mdi:playlist-check',
    component: BatchProcessor,
    props: {}
  }
]

// ==================== 计算属性 ====================
const displayStats = computed(() => ({
  totalProcessed: stats.value.totalProcessed,
  activeTasks: activeTasks.value.length,
  successRate: stats.value.successRate,
  totalSize: stats.value.totalSize
}))

const recentResults = computed(() => 
  allResults.value.slice(0, 10) // 显示最近10个结果
)

const hasProcessingTasks = computed(() => 
  activeTasks.value.some(task => task.status === 'processing')
)

const hasPausedTasks = computed(() => 
  activeTasks.value.some(task => task.status === 'paused')
)

const taskManagementToggleText = computed(() => 
  showTaskManagement.value ? '隐藏任务管理' : '显示任务管理'
)

// ==================== 模块任务统计 ====================
const getModuleTaskCount = (moduleName: string): number => {
  const typeMap: Record<string, string> = {
    'video': 'video-convert',
    'asr': 'asr',
    'image': 'image-process',
    'batch': 'batch'
  }
  
  const taskType = typeMap[moduleName]
  if (!taskType) return 0
  
  if (moduleName === 'batch') {
    return mediaStore.tasksStore.allBatchTasks.length
  }
  
  return activeTasks.value.filter(task => task.type === taskType).length
}

// ==================== 事件处理方法 ====================
const handleModuleChange = (moduleName: string) => {
  console.log(`[MediaPage] 切换到模块: ${moduleName}`)
  
  // 保存用户偏好
  localStorage.setItem('media-active-module', moduleName)
}

const handleFileUploaded = (payload: { name: string; size?: number; path: string; type: string }) => {
  console.log('[MediaPage] 文件已上传:', payload)
  ElMessage.success(`文件 "${payload.name}" 已添加`)
}

const handleTaskStarted = (taskId: string) => {
  console.log('[MediaPage] 任务已开始:', taskId)
  ElMessage.success('任务已开始处理')
}

const handleTaskCompleted = (result: any) => {
  console.log('[MediaPage] 任务已完成:', result)
  ElMessage.success('任务处理完成')
  refreshStats()
}

const handleError = (message: string) => {
  console.error('[MediaPage] 错误:', message)
  ElMessage.error(message)
}

const handleTaskAction = async (action: string, taskId: string) => {
  try {
    switch (action) {
      case 'pause':
        await mediaStore.pauseSingleTask(taskId)
        ElMessage.success('任务已暂停')
        break
      case 'resume':
        await mediaStore.retrySingleTask(taskId)
        ElMessage.success('任务已恢复')
        break
      case 'remove':
        await mediaStore.tasksStore.removeSingleTask(taskId)
        ElMessage.success('任务已删除')
        break
      case 'retry':
        await mediaStore.retrySingleTask(taskId)
        ElMessage.success('任务重试已开始')
        break
    }
  } catch (error: any) {
    ElMessage.error(`操作失败: ${error.message}`)
  }
}

const handleResultAction = (action: string, resultId: string) => {
  console.log(`[MediaPage] 结果操作: ${action}, ID: ${resultId}`)
  
  switch (action) {
    case 'view':
      // 查看结果详情
      break
    case 'download':
      // 下载结果文件
      break
    case 'remove':
      // 删除结果记录
      break
  }
}

// ==================== 任务管理方法 ====================
const pauseAllTasks = async () => {
  try {
    await mediaStore.pauseAllTasks()
    ElMessage.success('所有任务已暂停')
  } catch (error: any) {
    ElMessage.error(`暂停失败: ${error.message}`)
  }
}

const resumeAllTasks = async () => {
  try {
    // 恢复所有暂停的任务
    const pausedTasks = activeTasks.value.filter(task => task.status === 'paused')
    for (const task of pausedTasks) {
      await mediaStore.retrySingleTask(task.id)
    }
    ElMessage.success('所有任务已恢复')
  } catch (error: any) {
    ElMessage.error(`恢复失败: ${error.message}`)
  }
}

const clearAllTasks = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要清空所有任务吗？正在处理的任务将被终止。',
      '确认清空',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await mediaStore.tasksStore.clearAllTasks()
    ElMessage.success('所有任务已清空')
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error(`清空失败: ${error.message}`)
    }
  }
}

// ==================== 结果管理方法 ====================
const showAllResults = () => {
  // 跳转到结果页面或显示结果对话框
  console.log('[MediaPage] 显示所有结果')
}

const exportResults = () => {
  // 导出结果到文件
  console.log('[MediaPage] 导出结果')
  ElMessage.info('导出功能开发中...')
}

const clearResults = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要清空所有处理结果吗？',
      '确认清空',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await mediaStore.tasksStore.clearResults()
    ElMessage.success('结果已清空')
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error(`清空失败: ${error.message}`)
    }
  }
}

// ==================== 统计和设置方法 ====================
const refreshStats = async () => {
  isRefreshingStats.value = true
  try {
    await mediaStore.refreshStats()
    ElMessage.success('统计信息已刷新')
  } catch (error: any) {
    ElMessage.error(`刷新失败: ${error.message}`)
  } finally {
    isRefreshingStats.value = false
  }
}

const clearStats = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要清空统计数据吗？此操作不可恢复。',
      '确认清空',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await mediaStore.statsStore.resetStats()
    ElMessage.success('统计数据已清空')
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error(`清空失败: ${error.message}`)
    }
  }
}

const showGlobalSettings = () => {
  settingsVisible.value = true
}

const showHelp = () => {
  helpVisible.value = true
}

const toggleTaskManagement = () => {
  showTaskManagement.value = !showTaskManagement.value
  localStorage.setItem('media-show-task-management', showTaskManagement.value.toString())
}

const handleSettingsSaved = () => {
  ElMessage.success('设置已保存')
  refreshStats()
}

// ==================== 生命周期 ====================
let statsRefreshInterval: NodeJS.Timeout | null = null

onMounted(async () => {
  try {
    // 初始化 Store
    if (!isInitialized.value) {
      await mediaStore.initialize()
    }
    
    // 恢复用户偏好
    const savedModule = localStorage.getItem('media-active-module')
    if (savedModule && moduleConfigs.some(m => m.name === savedModule)) {
      activeModule.value = savedModule
    }
    
    const savedTaskManagement = localStorage.getItem('media-show-task-management')
    if (savedTaskManagement !== null) {
      showTaskManagement.value = savedTaskManagement === 'true'
    }
    
    // 初始统计刷新
    await refreshStats()
    
    // 设置定期刷新
    statsRefreshInterval = setInterval(async () => {
      if (!isRefreshingStats.value) {
        await mediaStore.refreshStats()
      }
    }, 30000) // 每30秒刷新
    
  } catch (error) {
    console.error('[MediaPage] 初始化失败:', error)
    ElMessage.error('页面初始化失败，请刷新重试')
  }
})

onUnmounted(() => {
  if (statsRefreshInterval) {
    clearInterval(statsRefreshInterval)
  }
})

// 暴露给开发工具的方法
if (process.env.NODE_ENV === 'development') {
  (window as any).mediaPageDebug = {
    refreshStats,
    clearAllTasks,
    clearResults,
    mediaStore
  }
}
</script>

<style lang="scss" scoped>
@import '@/components/media/shared/media-common';

.media-page {
  max-width: 1400px;
  margin: 0 auto;
  padding: $spacing-base;
  
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: $spacing-lg;
    
    .header-left {
      .page-title {
        display: flex;
        align-items: center;
        gap: $spacing-sm;
        margin: 0 0 $spacing-xs 0;
        color: $text-primary;
        font-size: 28px;
        font-weight: 600;
        
        .title-icon {
          color: $color-primary;
          font-size: 32px;
        }
      }
      
      .page-description {
        margin: 0;
        color: $text-secondary;
        font-size: 14px;
      }
    }
    
    .header-right {
      min-width: 300px;
    }
  }
  
  .modules-container {
    margin-bottom: $spacing-lg;
    
    .module-tabs {
      :deep(.el-tabs__header) {
        margin-bottom: $spacing-base;
      }
      
      .tab-label {
        display: flex;
        align-items: center;
        gap: $spacing-xs;
        
        .tab-icon {
          font-size: 16px;
        }
        
        .task-badge {
          margin-left: $spacing-xs;
        }
      }
    }
  }
  
  .task-management {
    display: grid;
    gap: $spacing-base;
    margin-bottom: $spacing-lg;
    
    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: $spacing-base;
      
      h3 {
        display: flex;
        align-items: center;
        gap: $spacing-xs;
        margin: 0;
        color: $text-primary;
        font-size: 16px;
        font-weight: 500;
      }
      
      .header-actions {
        .el-button-group .el-button {
          margin-left: 0;
        }
      }
    }
  }
  
  .stats-panel {
    margin-bottom: $spacing-lg;
  }
  
  .floating-toolbar {
    position: fixed;
    right: $spacing-base;
    bottom: $spacing-base;
    z-index: 1000;
    
    .el-button-group {
      display: flex;
      flex-direction: column;
      box-shadow: $box-shadow-dark;
      border-radius: $border-radius;
      overflow: hidden;
      
      .el-button {
        margin-left: 0;
        margin-bottom: 0;
        border-radius: 0;
        
        &:not(:last-child) {
          border-bottom: 1px solid $border-light;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .media-page {
    .page-header {
      flex-direction: column;
      gap: $spacing-base;
      
      .header-right {
        width: 100%;
        min-width: auto;
      }
    }
  }
}

@media (max-width: 768px) {
  .media-page {
    padding: $spacing-sm;
    
    .floating-toolbar {
      right: $spacing-sm;
      bottom: $spacing-sm;
    }
    
    .task-management .section-header {
      flex-direction: column;
      gap: $spacing-sm;
      align-items: flex-start;
    }
  }
}
</style>