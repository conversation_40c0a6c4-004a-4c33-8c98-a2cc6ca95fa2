<template>
  <div class="file-uploader">
    <el-upload
      ref="uploadRef"
      class="upload-area"
      drag
      multiple
      :auto-upload="false"
      :file-list="modelValue"
      :accept="acceptedFormats"
      :before-upload="handleBeforeUpload"
      :on-change="handleFileChange"
      :on-remove="handleFileRemove"
      list-type="text"
    >
      <div class="upload-content">
        <div class="upload-icons" v-if="showIcons">
          <Icon v-for="icon in uploadIcons" :key="icon.name" :icon="icon.icon" :class="icon.class" />
        </div>
        <div class="upload-text">{{ uploadText }}</div>
        <div class="upload-hint" v-if="hint">{{ hint }}</div>
      </div>
    </el-upload>
    
    <!-- 文件列表扩展信息 -->
    <div class="file-list-info" v-if="modelValue.length > 0">
      <div class="file-stats">
        <span class="stat-item">
          <Icon icon="mdi:file-multiple" />
          {{ modelValue.length }} 个文件
        </span>
        <span class="stat-item" v-if="totalSize > 0">
          <Icon icon="mdi:harddisk" />
          {{ formatFileSize(totalSize) }}
        </span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Icon } from '@iconify/vue'
import type { UploadFile, UploadRawFile } from 'element-plus'

// Props
interface Props {
  modelValue: UploadFile[]
  taskType?: 'video-convert' | 'audio-extract' | 'asr' | 'image-process'
  maxFileSize?: number // MB
  maxFileCount?: number
  uploadText?: string
  hint?: string
  showIcons?: boolean
  customAccept?: string
}

const props = withDefaults(defineProps<Props>(), {
  taskType: 'video-convert',
  maxFileSize: 1024, // 1GB
  maxFileCount: 20,
  uploadText: '拖拽文件到此处或点击上传',
  hint: '',
  showIcons: true,
  customAccept: ''
})

// Emits
const emit = defineEmits<{
  'update:modelValue': [files: UploadFile[]]
  'fileAdded': [file: UploadFile, filePath: string]
  'fileRemoved': [file: UploadFile]
  'error': [message: string]
}>()

// 响应式数据
const uploadRef = ref()
const filePathMap = ref<Map<string, string>>(new Map())

// 计算属性
const acceptedFormats = computed(() => {
  if (props.customAccept) return props.customAccept
  
  const formats = {
    'video-convert': '.mp4,.avi,.mov,.mkv,.flv,.wmv,.m4v',
    'audio-extract': '.mp4,.avi,.mov,.mkv,.flv,.wmv,.m4v',
    'asr': '.mp3,.wav,.m4a,.aac,.flac,.ogg,.mp4,.avi,.mov,.mkv,.flv,.wmv',
    'image-process': '.jpg,.jpeg,.png,.webp,.bmp,.gif,.tiff,.svg'
  }
  return formats[props.taskType] || '*'
})

const uploadIcons = computed(() => {
  const iconConfigs = {
    'video-convert': [
      { name: 'video', icon: 'mdi:video', class: 'upload-icon video-icon' }
    ],
    'audio-extract': [
      { name: 'video', icon: 'mdi:video', class: 'upload-icon video-icon' }
    ],
    'asr': [
      { name: 'audio', icon: 'mdi:music', class: 'upload-icon audio-icon' },
      { name: 'video', icon: 'mdi:video', class: 'upload-icon video-icon' }
    ],
    'image-process': [
      { name: 'image', icon: 'mdi:image', class: 'upload-icon image-icon' }
    ]
  }
  return iconConfigs[props.taskType] || []
})

const totalSize = computed(() => {
  return props.modelValue.reduce((sum, file) => sum + (file.size || 0), 0)
})

// 文件处理方法
const handleBeforeUpload = (rawFile: UploadRawFile): boolean => {
  // 检查文件数量
  if (props.modelValue.length >= props.maxFileCount) {
    emit('error', `最多只能上传 ${props.maxFileCount} 个文件`)
    return false
  }

  // 检查文件类型
  if (!validateFileType(rawFile)) {
    emit('error', '文件格式不支持')
    return false
  }

  // 检查文件大小
  const fileSizeMB = rawFile.size / 1024 / 1024
  if (fileSizeMB > props.maxFileSize) {
    emit('error', `文件大小不能超过 ${props.maxFileSize}MB`)
    return false
  }

  return false // 阻止自动上传
}

const handleFileChange = (file: UploadFile) => {
  if (file.status === 'ready') {
    console.log('文件添加:', file.name)

    // 确保文件被添加到列表中
    const updatedFiles = [...props.modelValue]
    if (!updatedFiles.some(f => f.uid === file.uid)) {
      updatedFiles.push(file)
    }

    // 获取文件真实路径
    if (file.raw) {
      try {
        const filePath = window.electronAPI.media.getPathForFile(file.raw)
        filePathMap.value.set(file.uid.toString(), filePath)
        emit('fileAdded', file, filePath)
      } catch (error: any) {
        console.error('获取文件路径失败:', error)
        emit('error', `获取文件路径失败: ${error.message || '未知错误'}`)
        return
      }
    }

    emit('update:modelValue', updatedFiles)
  }
}

const handleFileRemove = (file: UploadFile) => {
  console.log('文件移除:', file.name)
  
  // 清理文件路径映射
  filePathMap.value.delete(file.uid.toString())
  
  // 更新文件列表
  const updatedFiles = props.modelValue.filter(f => f.uid !== file.uid)
  emit('update:modelValue', updatedFiles)
  emit('fileRemoved', file)
}

// 文件类型验证
const validateFileType = (file: UploadRawFile): boolean => {
  const fileName = file.name.toLowerCase()
  const fileExt = '.' + fileName.split('.').pop()
  
  // 基于MIME类型验证
  const mimeValidations = {
    'video-convert': () => file.type.startsWith('video/'),
    'audio-extract': () => file.type.startsWith('video/'),
    'asr': () => file.type.startsWith('audio/') || file.type.startsWith('video/'),
    'image-process': () => file.type.startsWith('image/')
  }

  const mimeValidator = mimeValidations[props.taskType]
  if (mimeValidator && !mimeValidator()) {
    // 如果MIME类型验证失败，尝试文件扩展名验证
    return acceptedFormats.value.includes(fileExt)
  }

  return true
}

// 工具方法
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return (bytes / Math.pow(k, i)).toFixed(1) + ' ' + sizes[i]
}

// 公开方法
const clearFiles = () => {
  filePathMap.value.clear()
  emit('update:modelValue', [])
}

const getFilePath = (fileUid: string): string | undefined => {
  return filePathMap.value.get(fileUid)
}

// 暴露方法给父组件
defineExpose({
  clearFiles,
  getFilePath,
  filePathMap: filePathMap.value
})

// 监听modelValue变化，同步内部状态
watch(() => props.modelValue, (newFiles) => {
  // 清理已移除文件的路径映射
  const fileUids = new Set(newFiles.map(f => f.uid.toString()))
  for (const [uid] of filePathMap.value) {
    if (!fileUids.has(uid)) {
      filePathMap.value.delete(uid)
    }
  }
}, { deep: true })
</script>

<style scoped lang="scss">
@use '@/assets/styles/index.scss' as *;

.file-uploader {
  .upload-area {
    :deep(.el-upload-dragger) {
      background: linear-gradient(135deg, #f6f9ff 0%, #f0f5ff 100%);
      border: 2px dashed #d9d9d9;
      border-radius: $border-radius-base;
      transition: all 0.3s ease;

      &:hover {
        border-color: $primary-color;
        background: linear-gradient(135deg, #fff2f4 0%, #fff0f1 100%);
      }
    }

    .upload-content {
      padding: $spacing-xl;
      text-align: center;

      .upload-icons {
        display: flex;
        justify-content: center;
        gap: $spacing-base;
        margin-bottom: $spacing-base;

        .upload-icon {
          font-size: 48px;
          margin-bottom: 0;

          &.video-icon {
            color: #722ed1;
          }

          &.audio-icon {
            color: #13c2c2;
          }

          &.image-icon {
            color: #52c41a;
          }
        }
      }

      .upload-text {
        font-size: 16px;
        color: $text-primary;
        margin-bottom: $spacing-small;
      }

      .upload-hint {
        font-size: 12px;
        color: $text-secondary;
      }
    }
  }

  .file-list-info {
    margin-top: $spacing-base;
    
    .file-stats {
      display: flex;
      gap: $spacing-base;
      justify-content: center;
      
      .stat-item {
        display: flex;
        align-items: center;
        gap: $spacing-small;
        font-size: 12px;
        color: $text-secondary;
        padding: $spacing-small $spacing-base;
        background: $background-light;
        border-radius: $border-radius-small;
      }
    }
  }
}
</style>