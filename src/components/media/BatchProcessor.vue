<template>
  <div class="app-card batch-processor">
    <div class="section-header">
      <h3>
        <Icon icon="mdi:playlist-check" class="section-icon" />
        批量处理中心
      </h3>
      <div class="header-actions">
        <el-button size="small" @click="refreshBatchList">
          <Icon icon="mdi:refresh" />
          刷新
        </el-button>
        <el-button size="small" type="danger" @click="clearAllBatches" v-if="allBatches.length > 0">
          <Icon icon="mdi:trash-can-outline" />
          清空所有
        </el-button>
      </div>
    </div>

    <!-- 批量任务创建区域 -->
    <div class="create-section" v-if="!currentBatch">
      <el-divider>创建新的批量任务</el-divider>

      <div class="create-form">
        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="任务名称">
              <el-input 
                v-model="batchForm.name" 
                placeholder="输入批量任务名称"
                :maxlength="50"
                show-word-limit
              />
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="处理类型">
              <el-select 
                v-model="batchForm.type" 
                placeholder="选择处理类型"
                @change="handleTaskTypeChange"
              >
                <el-option label="视频转换" value="video-convert" />
                <el-option label="音频提取" value="audio-extract" />
                <el-option label="语音识别" value="asr" />
                <el-option label="图片处理" value="image-process" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 文件上传 -->
        <el-form-item label="选择文件">
          <FileUploader
            v-model="batchForm.files"
            :task-type="batchForm.type"
            :max-file-count="100"
            :upload-text="getUploadText()"
            :hint="getUploadHint()"
            @file-added="handleFileAdded"
            @file-removed="handleFileRemoved"
            @error="handleError"
          />
        </el-form-item>

        <!-- 处理选项 -->
        <el-form-item label="处理选项" v-if="batchForm.type">
          <ProcessingOptions
            v-model="batchForm.options"
            :task-type="batchForm.type"
            :compact="true"
            :show-presets="true"
          />
        </el-form-item>

        <!-- 输出目录 -->
        <el-form-item label="输出目录">
          <OutputDirectorySelector
            v-model="batchForm.outputDirectory"
            :show-validation="true"
            @error="handleError"
          />
        </el-form-item>

        <!-- 高级设置 -->
        <div class="advanced-settings" v-if="showAdvancedSettings">
          <el-divider>高级设置</el-divider>
          
          <el-row :gutter="16">
            <el-col :span="12">
              <el-form-item label="并发数量">
                <el-slider
                  v-model="batchForm.concurrency"
                  :min="1"
                  :max="8"
                  :step="1"
                  show-stops
                  show-input
                />
              </el-form-item>
            </el-col>
            
            <el-col :span="12">
              <el-form-item label="失败处理">
                <el-radio-group v-model="batchForm.failureHandling">
                  <el-radio label="continue">继续处理其他文件</el-radio>
                  <el-radio label="stop">失败时停止所有</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="24">
              <el-form-item label="其他选项">
                <el-checkbox-group v-model="batchForm.extraOptions">
                  <el-checkbox label="auto-start">创建后自动开始</el-checkbox>
                  <el-checkbox label="notification">完成后通知</el-checkbox>
                  <el-checkbox label="cleanup">处理后清理临时文件</el-checkbox>
                </el-checkbox-group>
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- 操作按钮 -->
        <div class="form-actions">
          <el-button 
            type="primary" 
            @click="createBatchTask" 
            :disabled="!canCreateBatch"
            :loading="creating"
          >
            <Icon icon="mdi:plus-circle" />
            创建批量任务
          </el-button>
          
          <el-button @click="toggleAdvancedSettings">
            <Icon :icon="showAdvancedSettings ? 'mdi:chevron-up' : 'mdi:chevron-down'" />
            {{ showAdvancedSettings ? '隐藏' : '显示' }}高级设置
          </el-button>
          
          <el-button @click="resetForm" v-if="batchForm.files.length > 0">
            <Icon icon="mdi:refresh" />
            重置表单
          </el-button>
        </div>
      </div>
    </div>

    <!-- 当前批量任务详情 -->
    <div class="current-batch-section" v-if="currentBatch">
      <el-divider>{{ currentBatch.name }}</el-divider>

      <!-- 批量任务信息卡片 -->
      <div class="batch-info-card">
        <div class="batch-header">
          <div class="batch-title">
            <Icon :icon="getTaskTypeIcon(currentBatch.type)" />
            <span>{{ getTaskTypeLabel(currentBatch.type) }}</span>
            <el-tag :type="getBatchStatusType(currentBatch.status)" size="small">
              {{ getBatchStatusLabel(currentBatch.status) }}
            </el-tag>
          </div>
          
          <div class="batch-meta">
            <span class="meta-item">
              <Icon icon="mdi:calendar-clock" />
              创建时间: {{ formatTime(currentBatch.createdAt) }}
            </span>
            <span class="meta-item" v-if="currentBatch.startTime">
              <Icon icon="mdi:play-circle" />
              开始时间: {{ formatTime(currentBatch.startTime) }}
            </span>
            <span class="meta-item" v-if="currentBatch.completedAt">
              <Icon icon="mdi:check-circle" />
              完成时间: {{ formatTime(currentBatch.completedAt) }}
            </span>
          </div>
        </div>

        <!-- 批量任务统计 -->
        <div class="batch-stats">
          <div class="stat-item">
            <Icon icon="mdi:file-multiple" class="stat-icon" />
            <span class="stat-label">总任务数</span>
            <span class="stat-value">{{ currentBatch.totalTasks }}</span>
          </div>

          <div class="stat-item">
            <Icon icon="mdi:check-circle" class="stat-icon success" />
            <span class="stat-label">已完成</span>
            <span class="stat-value">{{ currentBatch.completedTasks }}</span>
          </div>

          <div class="stat-item">
            <Icon icon="mdi:clock" class="stat-icon processing" />
            <span class="stat-label">进行中</span>
            <span class="stat-value">{{ getProcessingTasksCount(currentBatch) }}</span>
          </div>

          <div class="stat-item">
            <Icon icon="mdi:alert-circle" class="stat-icon error" />
            <span class="stat-label">失败</span>
            <span class="stat-value">{{ currentBatch.failedTasks }}</span>
          </div>
        </div>

        <!-- 整体进度条 -->
        <div class="batch-progress">
          <div class="progress-info">
            <span>整体进度: {{ currentBatch.progress }}%</span>
            <span>({{ currentBatch.completedTasks + currentBatch.failedTasks }}/{{ currentBatch.totalTasks }})</span>
          </div>
          <el-progress 
            :percentage="currentBatch.progress" 
            :stroke-width="12" 
            :status="getBatchProgressStatus(currentBatch)"
          />
        </div>
      </div>

      <!-- 批量任务控制 -->
      <TaskControls
        mode="batch"
        :status="currentBatch.status === 'pending' ? 'idle' : currentBatch.status"
        :is-processing="currentBatch.status === 'processing'"
        :can-start="canStartBatch"
        :can-pause="canPauseBatch"
        :can-stop="canStopBatch"
        :total-tasks="currentBatch.totalTasks"
        :completed-tasks="currentBatch.completedTasks"
        :failed-tasks-count="currentBatch.failedTasks"
        :overall-progress="currentBatch.progress"
        :show-batch-controls="true"
        :show-retry-failed="currentBatch.failedTasks > 0"
        :show-advanced="true"
        :show-concurrency-control="true"
        :current-concurrency="batchConcurrency"
        :max-concurrency="8"
        @start="startBatch"
        @pause="pauseBatch"
        @stop="stopBatch"
        @retry-failed="retryFailedTasks"
        @clear-all="confirmClearBatch"
        @set-concurrency="setBatchConcurrency"
      />

      <!-- 子任务列表 -->
      <div class="subtasks-section">
        <el-divider>子任务列表</el-divider>
        
        <!-- 任务过滤器 -->
        <div class="task-filter">
          <el-button-group>
            <el-button 
              :type="taskFilter === 'all' ? 'primary' : 'default'" 
              @click="taskFilter = 'all'"
            >
              全部 ({{ currentBatch.tasks.length }})
            </el-button>
            <el-button 
              :type="taskFilter === 'pending' ? 'primary' : 'default'" 
              @click="taskFilter = 'pending'"
            >
              等待中 ({{ getPendingTasksCount(currentBatch) }})
            </el-button>
            <el-button 
              :type="taskFilter === 'processing' ? 'primary' : 'default'" 
              @click="taskFilter = 'processing'"
            >
              处理中 ({{ getProcessingTasksCount(currentBatch) }})
            </el-button>
            <el-button 
              :type="taskFilter === 'completed' ? 'primary' : 'default'" 
              @click="taskFilter = 'completed'"
            >
              已完成 ({{ currentBatch.completedTasks }})
            </el-button>
            <el-button 
              :type="taskFilter === 'error' ? 'primary' : 'default'" 
              @click="taskFilter = 'error'"
            >
              失败 ({{ currentBatch.failedTasks }})
            </el-button>
          </el-button-group>
        </div>

        <!-- 子任务进度展示 -->
        <TaskProgress
          :tasks="filteredSubTasks"
          :allow-retry="true"
          :allow-pause="true"
          :allow-remove="true"
          :show-task-type="false"
          :show-summary="false"
          :max-display-tasks="20"
          @retry="retrySubTask"
          @pause="pauseSubTask"
          @remove="removeSubTask"
          @view-result="viewSubTaskResult"
        />
      </div>

      <!-- 批量任务结果 -->
      <div class="batch-results-section" v-if="batchResults.length > 0">
        <el-divider>处理结果</el-divider>
        <TaskResultPanel
          :results="batchResults"
          :allow-retry="false"
          :show-pagination="true"
          :page-size="10"
          empty-text="暂无批量处理结果"
          @remove="removeBatchResult"
          @export-all="exportBatchResults"
        />
      </div>
    </div>

    <!-- 批量任务历史列表 -->
    <div class="batch-history-section" v-if="!currentBatch && allBatches.length > 0">
      <el-divider>批量任务历史</el-divider>
      
      <div class="batch-list">
        <div 
          v-for="batch in paginatedBatches" 
          :key="batch.id"
          class="batch-item"
          :class="{ 'batch-active': batch.id === currentBatch?.id }"
          @click="selectBatch(batch)"
        >
          <div class="batch-item-header">
            <div class="batch-item-title">
              <Icon :icon="getTaskTypeIcon(batch.type)" />
              <span class="batch-name">{{ batch.name }}</span>
              <el-tag :type="getBatchStatusType(batch.status)" size="small">
                {{ getBatchStatusLabel(batch.status) }}
              </el-tag>
            </div>
            
            <div class="batch-item-actions">
              <el-button 
                size="small" 
                v-if="['pending', 'paused'].includes(batch.status)"
                @click.stop="continueBatch(batch)"
              >
                <Icon icon="mdi:play" />
                继续
              </el-button>
              
              <el-button 
                size="small" 
                @click.stop="viewBatchDetail(batch)"
              >
                <Icon icon="mdi:eye" />
                查看
              </el-button>
              
              <el-button 
                size="small" 
                type="danger"
                @click.stop="deleteBatch(batch)"
              >
                <Icon icon="mdi:delete" />
                删除
              </el-button>
            </div>
          </div>

          <div class="batch-item-info">
            <div class="batch-meta">
              <span class="meta-item">{{ batch.totalTasks }} 个文件</span>
              <span class="meta-item">{{ formatTime(batch.createdAt) }}</span>
              <span class="meta-item" v-if="batch.completedAt">
                耗时: {{ formatDuration(batch.completedAt - (batch.startTime || batch.createdAt)) }}
              </span>
            </div>
            
            <div class="batch-progress-mini">
              <el-progress 
                :percentage="batch.progress" 
                :stroke-width="4"
                :show-text="false"
                :status="getBatchProgressStatus(batch)"
              />
              <span class="progress-text">{{ batch.progress }}%</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 分页控制 -->
      <div class="pagination-wrapper" v-if="allBatches.length > pageSize">
        <el-pagination
          v-model:current-page="currentPage"
          :page-size="pageSize"
          :total="allBatches.length"
          layout="prev, pager, next, total"
          small
        />
      </div>
    </div>

    <!-- 空状态 -->
    <div class="empty-state" v-if="!currentBatch && allBatches.length === 0">
      <Icon icon="mdi:playlist-remove" class="empty-icon" />
      <div class="empty-text">暂无批量任务</div>
      <div class="empty-hint">创建批量任务来并行处理多个文件</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Icon } from '@iconify/vue'
import type { UploadFile } from 'element-plus'

// 导入共享组件
import FileUploader from './shared/FileUploader.vue'
import OutputDirectorySelector from './shared/OutputDirectorySelector.vue'
import ProcessingOptions from './shared/ProcessingOptions.vue'
import TaskControls from './shared/TaskControls.vue'
import TaskProgress from './shared/TaskProgress.vue'
import TaskResultPanel from './shared/TaskResultPanel.vue'

// 导入Store
import { useMediaStore } from '@/stores/media-store'
import type { BatchTask, ProcessingOptions as ProcessingOptionsType } from '@/stores/media-store'

// 响应式数据
const mediaStore = useMediaStore()
const currentBatch = ref<BatchTask | null>(null)
const taskFilter = ref('all')
const showAdvancedSettings = ref(false)
const creating = ref(false)
const batchConcurrency = ref(3)
const currentPage = ref(1)
const pageSize = ref(10)
const filePathMap = ref<Map<string, string>>(new Map())

// 批量任务表单
const batchForm = reactive({
  name: '',
  type: '' as 'video-convert' | 'audio-extract' | 'asr' | 'image-process',
  files: [] as UploadFile[],
  outputDirectory: '',
  options: {} as ProcessingOptionsType,
  concurrency: 3,
  failureHandling: 'continue' as 'continue' | 'stop',
  extraOptions: [] as string[]
})

// 计算属性
const allBatches = computed(() => mediaStore.allBatchTasks)

const paginatedBatches = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return allBatches.value.slice(start, end)
})

const canCreateBatch = computed(() => {
  return batchForm.name.trim() &&
    batchForm.type &&
    batchForm.files.length > 0 &&
    batchForm.outputDirectory &&
    !creating.value
})

const canStartBatch = computed(() => {
  return !!(currentBatch.value && 
    ['pending', 'paused'].includes(currentBatch.value.status) &&
    getPendingTasksCount(currentBatch.value) > 0)
})

const canPauseBatch = computed(() => {
  return !!(currentBatch.value && currentBatch.value.status === 'processing')
})

const canStopBatch = computed(() => {
  return !!(currentBatch.value && ['processing', 'paused'].includes(currentBatch.value.status))
})

const filteredSubTasks = computed(() => {
  if (!currentBatch.value) return []
  
  if (taskFilter.value === 'all') {
    return currentBatch.value.tasks
  }
  
  return currentBatch.value.tasks.filter(task => task.status === taskFilter.value)
})

const batchResults = computed(() => {
  if (!currentBatch.value) return []
  
  return currentBatch.value.tasks
    .filter(task => task.result)
    .map(task => task.result)
})

// 事件处理方法
const handleTaskTypeChange = () => {
  // 任务类型变化时重置文件和选项
  batchForm.files = []
  batchForm.options = {}
  filePathMap.value.clear()
}

const handleFileAdded = (file: UploadFile, filePath: string) => {
  filePathMap.value.set(file.uid.toString(), filePath)
  
  // 设置默认输出目录
  if (!batchForm.outputDirectory) {
    batchForm.outputDirectory = mediaStore.settings.defaultOutputDir
  }
  
  // 自动生成任务名称
  if (!batchForm.name) {
    const taskTypeLabel = getTaskTypeLabel(batchForm.type)
    batchForm.name = `${taskTypeLabel}批量任务_${new Date().toLocaleString()}`
  }
}

const handleFileRemoved = (file: UploadFile) => {
  filePathMap.value.delete(file.uid.toString())
}

const handleError = (message: string) => {
  ElMessage.error(message)
}

// 批量任务管理方法
const createBatchTask = async () => {
  if (!canCreateBatch.value) {
    ElMessage.warning('请完善任务信息')
    return
  }

  creating.value = true

  try {
    const files = batchForm.files.map(file => ({
      fileName: file.name,
      filePath: filePathMap.value.get(file.uid.toString()) || file.name
    }))

    const batchId = await mediaStore.createBatchTask({
      name: batchForm.name,
      type: batchForm.type,
      files,
      outputDirectory: batchForm.outputDirectory,
      options: JSON.parse(JSON.stringify(batchForm.options))
    })

    // 获取创建的批量任务
    const batch = mediaStore.batchTasks.get(batchId)
    if (batch) {
      currentBatch.value = batch
      
      // 如果设置了自动开始
      if (batchForm.extraOptions.includes('auto-start')) {
        await startBatch()
      }
    }

    ElMessage.success('批量任务创建成功')
    resetForm()
    
  } catch (error: any) {
    ElMessage.error(`创建批量任务失败: ${error.message}`)
  } finally {
    creating.value = false
  }
}

const startBatch = async () => {
  if (!currentBatch.value) return

  try {
    await mediaStore.startBatchTask(currentBatch.value.id, {
      concurrency: batchConcurrency.value
    })
    
    ElMessage.success('批量任务已开始')
  } catch (error: any) {
    ElMessage.error(`启动批量任务失败: ${error.message}`)
  }
}

const pauseBatch = async () => {
  if (!currentBatch.value) return

  try {
    await mediaStore.pauseBatchTask(currentBatch.value.id)
    ElMessage.info('批量任务已暂停')
  } catch (error: any) {
    ElMessage.error(`暂停批量任务失败: ${error.message}`)
  }
}

const stopBatch = async () => {
  if (!currentBatch.value) return

  try {
    await ElMessageBox.confirm(
      '确定要停止当前批量任务吗？正在处理的任务将被取消。',
      '确认停止',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await pauseBatch()
    
    // 重置所有子任务状态
    currentBatch.value.tasks.forEach(task => {
      if (['processing', 'paused'].includes(task.status)) {
        task.status = 'pending'
        task.progress = 0
      }
    })
    
    ElMessage.info('批量任务已停止')
    
  } catch {
    // 用户取消
  }
}

const retryFailedTasks = async () => {
  if (!currentBatch.value) return

  try {
    await mediaStore.retryBatchFailedTasks(currentBatch.value.id)
    ElMessage.success('失败任务重试已开始')
  } catch (error: any) {
    ElMessage.error(`重试失败任务失败: ${error.message}`)
  }
}

const confirmClearBatch = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要清空当前批量任务吗？这将删除所有子任务和结果。',
      '确认清空',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    if (currentBatch.value) {
      await mediaStore.removeBatchTask(currentBatch.value.id)
      currentBatch.value = null
      ElMessage.success('批量任务已清空')
    }
  } catch {
    // 用户取消
  }
}

const setBatchConcurrency = (concurrency: number) => {
  batchConcurrency.value = concurrency
}

// 子任务操作方法
const retrySubTask = async (task: any) => {
  if (!currentBatch.value) return

  try {
    // 重置子任务状态
    task.status = 'pending'
    task.progress = 0
    task.error = undefined
    
    // 如果批量任务不是处理中状态，启动它
    if (currentBatch.value.status !== 'processing') {
      await startBatch()
    }
    
    ElMessage.success('子任务重试已开始')
  } catch (error: any) {
    ElMessage.error(`重试子任务失败: ${error.message}`)
  }
}

const pauseSubTask = async (task: any) => {
  // 子任务暂停实际上就是暂停整个批量任务
  await pauseBatch()
}

const removeSubTask = async (task: any) => {
  if (!currentBatch.value) return

  try {
    await ElMessageBox.confirm(
      `确定要移除子任务 "${task.fileName}" 吗？`,
      '确认移除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await mediaStore.removeBatchSubTask(currentBatch.value.id, task.id)
    ElMessage.success('子任务已移除')
    
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error(`移除子任务失败: ${error.message}`)
    }
  }
}

const viewSubTaskResult = async (task: any) => {
  if (task.result?.outputPath) {
    try {
      await window.electronAPI.app.showItemInFolder(task.result.outputPath)
    } catch (error: any) {
      ElMessage.error(`打开文件失败: ${error.message}`)
    }
  }
}

// 批量任务历史操作方法
const selectBatch = (batch: BatchTask) => {
  currentBatch.value = batch
  taskFilter.value = 'all'
}

const continueBatch = async (batch: BatchTask) => {
  currentBatch.value = batch
  await startBatch()
}

const viewBatchDetail = (batch: BatchTask) => {
  currentBatch.value = batch
  taskFilter.value = 'all'
}

const deleteBatch = async (batch: BatchTask) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除批量任务 "${batch.name}" 吗？这将删除所有相关数据。`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await mediaStore.removeBatchTask(batch.id)
    
    // 如果删除的是当前任务，清空当前任务
    if (currentBatch.value?.id === batch.id) {
      currentBatch.value = null
    }
    
    ElMessage.success('批量任务已删除')
    
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error(`删除批量任务失败: ${error.message}`)
    }
  }
}

// 结果操作方法
const removeBatchResult = (result: any) => {
  const index = batchResults.value.findIndex(r => r?.id === result.id)
  if (index > -1) {
    // 从对应的子任务中移除结果引用
    const task = currentBatch.value?.tasks.find(t => t.result?.id === result.id)
    if (task) {
      task.result = undefined
    }
  }
}

const exportBatchResults = () => {
  // TODO: 实现批量结果导出
  ElMessage.info('批量导出功能开发中...')
}

// 工具方法
const refreshBatchList = async () => {
  try {
    await mediaStore.refreshStats()
    ElMessage.success('批量任务列表已刷新')
  } catch (error: any) {
    ElMessage.error(`刷新失败: ${error.message}`)
  }
}

const clearAllBatches = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要清空所有批量任务吗？这将删除所有批量任务及其结果。',
      '确认清空',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    // 逐个删除所有批量任务
    for (const batch of allBatches.value) {
      await mediaStore.removeBatchTask(batch.id)
    }
    
    currentBatch.value = null
    ElMessage.success('所有批量任务已清空')
    
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error(`清空失败: ${error.message}`)
    }
  }
}

const toggleAdvancedSettings = () => {
  showAdvancedSettings.value = !showAdvancedSettings.value
}

const resetForm = () => {
  batchForm.name = ''
  batchForm.type = '' as any
  batchForm.files = []
  batchForm.outputDirectory = ''
  batchForm.options = {}
  batchForm.concurrency = 3
  batchForm.failureHandling = 'continue'
  batchForm.extraOptions = []
  filePathMap.value.clear()
}

const getUploadText = (): string => {
  if (!batchForm.type) return '请先选择处理类型'
  
  const texts = {
    'video-convert': '拖拽视频文件到此处或点击上传',
    'audio-extract': '拖拽视频文件到此处或点击上传',
    'asr': '拖拽音频或视频文件到此处或点击上传',
    'image-process': '拖拽图片文件到此处或点击上传'
  }
  
  return texts[batchForm.type] || '拖拽文件到此处或点击上传'
}

const getUploadHint = (): string => {
  const hints = {
    'video-convert': '支持 MP4, AVI, MOV, MKV, FLV, WMV 格式',
    'audio-extract': '支持 MP4, AVI, MOV, MKV, FLV, WMV 格式',
    'asr': '音频: MP3, WAV, M4A, AAC, FLAC；视频: MP4, AVI, MOV, MKV',
    'image-process': '支持 JPG, PNG, WebP, BMP, GIF, TIFF, SVG 格式'
  }
  
  return hints[batchForm.type] || ''
}

const getTaskTypeIcon = (type: string): string => {
  const icons = {
    'video-convert': 'mdi:video-outline',
    'audio-extract': 'mdi:music-note',
    'asr': 'mdi:microphone',
    'image-process': 'mdi:image-outline'
  }
  return icons[type] || 'mdi:file-outline'
}

const getTaskTypeLabel = (type: string): string => {
  const labels = {
    'video-convert': '视频转换',
    'audio-extract': '音频提取',
    'asr': '语音识别',
    'image-process': '图片处理'
  }
  return labels[type] || type
}

const getBatchStatusType = (status: string): any => {
  const types = {
    'pending': 'info',
    'processing': 'warning',
    'paused': 'info',
    'completed': 'success',
    'error': 'danger'
  }
  return types[status] || 'info'
}

const getBatchStatusLabel = (status: string): string => {
  const labels = {
    'pending': '等待中',
    'processing': '处理中',
    'paused': '已暂停',
    'completed': '已完成',
    'error': '失败'
  }
  return labels[status] || status
}

const getBatchProgressStatus = (batch: BatchTask) => {
  if (batch.failedTasks > 0) return 'exception'
  if (batch.status === 'completed') return 'success'
  return undefined
}

const getPendingTasksCount = (batch: BatchTask): number => {
  return batch.tasks.filter(t => t.status === 'pending').length
}

const getProcessingTasksCount = (batch: BatchTask): number => {
  return batch.tasks.filter(t => t.status === 'processing').length
}

const formatTime = (timestamp: number): string => {
  return new Date(timestamp).toLocaleString('zh-CN')
}

const formatDuration = (ms: number): string => {
  const seconds = Math.floor(ms / 1000)
  if (seconds < 60) return `${seconds}秒`
  if (seconds < 3600) return `${Math.floor(seconds / 60)}分${seconds % 60}秒`
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  return `${hours}时${minutes}分`
}

// 生命周期
onMounted(async () => {
  if (!mediaStore.isInitialized) {
    await mediaStore.initialize()
  }
  
  // 设置默认并发数
  batchConcurrency.value = mediaStore.settings.maxConcurrentTasks
})

// 监听当前批量任务状态变化
watch(() => currentBatch.value?.status, (newStatus) => {
  if (newStatus === 'completed') {
    ElMessage.success('批量任务处理完成')
    
    // 如果设置了通知选项
    const batch = currentBatch.value
    if (batch && batchForm.extraOptions.includes('notification')) {
      // TODO: 发送系统通知
    }
  }
})
</script>

<style scoped lang="scss">
@use '@/assets/styles/index.scss' as *;

.batch-processor {
  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: $spacing-base;

    h3 {
      display: flex;
      align-items: center;
      gap: 8px;
      margin: 0;
      color: $text-primary;
      font-size: 16px;
      font-weight: 500;

      .section-icon {
        color: #722ed1;
        font-size: 18px;
      }
    }

    .header-actions {
      display: flex;
      gap: $spacing-small;
    }
  }

  .create-section {
    .create-form {
      :deep(.el-form-item) {
        margin-bottom: $spacing-base;

        .el-form-item__label {
          color: $text-regular;
          font-weight: 500;
        }
      }

      .advanced-settings {
        margin-top: $spacing-base;
        padding: $spacing-base;
        background: $background-light;
        border-radius: $border-radius-base;
      }

      .form-actions {
        display: flex;
        gap: $spacing-base;
        justify-content: center;
        margin-top: $spacing-xl;
      }
    }
  }

  .current-batch-section {
    .batch-info-card {
      background: $background-light;
      border-radius: $border-radius-base;
      padding: $spacing-base;
      margin-bottom: $spacing-base;

      .batch-header {
        margin-bottom: $spacing-base;

        .batch-title {
          display: flex;
          align-items: center;
          gap: $spacing-small;
          margin-bottom: $spacing-small;

          .iconify {
            font-size: 20px;
            color: #722ed1;
          }

          span {
            font-size: 16px;
            font-weight: 500;
            color: $text-primary;
          }
        }

        .batch-meta {
          display: flex;
          gap: $spacing-base;
          flex-wrap: wrap;

          .meta-item {
            display: flex;
            align-items: center;
            gap: 4px;
            font-size: 12px;
            color: $text-secondary;
          }
        }
      }

      .batch-stats {
        display: flex;
        gap: $spacing-base;
        margin-bottom: $spacing-base;

        .stat-item {
          display: flex;
          align-items: center;
          gap: $spacing-small;
          padding: $spacing-small $spacing-base;
          background: $background-card;
          border-radius: $border-radius-base;
          flex: 1;

          .stat-icon {
            font-size: 16px;

            &.success { color: $success-color; }
            &.processing { color: $primary-color; }
            &.error { color: $danger-color; }
          }

          .stat-label {
            color: $text-secondary;
            font-size: 12px;
          }

          .stat-value {
            font-weight: 600;
            color: $text-primary;
            margin-left: auto;
          }
        }
      }

      .batch-progress {
        .progress-info {
          display: flex;
          justify-content: space-between;
          margin-bottom: $spacing-small;
          font-size: 13px;
          color: $text-secondary;
        }
      }
    }

    .subtasks-section {
      margin-top: $spacing-base;

      .task-filter {
        display: flex;
        justify-content: center;
        margin-bottom: $spacing-base;
      }
    }

    .batch-results-section {
      margin-top: $spacing-base;
    }
  }

  .batch-history-section {
    .batch-list {
      .batch-item {
        border: 1px solid $border-lighter;
        border-radius: $border-radius-base;
        margin-bottom: $spacing-small;
        background: $background-light;
        cursor: pointer;
        transition: $transition-base;

        &:hover {
          background: $background-card;
          border-color: $border-light;
        }

        &.batch-active {
          border-color: $primary-color;
          background: #f6f9ff;
        }

        .batch-item-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: $spacing-base;

          .batch-item-title {
            display: flex;
            align-items: center;
            gap: $spacing-small;
            flex: 1;

            .iconify {
              font-size: 16px;
              color: #722ed1;
            }

            .batch-name {
              font-weight: 500;
              color: $text-primary;
            }
          }

          .batch-item-actions {
            display: flex;
            gap: $spacing-small;
          }
        }

        .batch-item-info {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 0 $spacing-base $spacing-base;

          .batch-meta {
            display: flex;
            gap: $spacing-base;

            .meta-item {
              font-size: 12px;
              color: $text-secondary;
            }
          }

          .batch-progress-mini {
            display: flex;
            align-items: center;
            gap: $spacing-small;
            min-width: 120px;

            .el-progress {
              flex: 1;
            }

            .progress-text {
              font-size: 12px;
              color: $text-secondary;
              min-width: 30px;
            }
          }
        }
      }
    }

    .pagination-wrapper {
      display: flex;
      justify-content: center;
      margin-top: $spacing-base;
    }
  }

  .empty-state {
    text-align: center;
    padding: $spacing-xl;
    color: $text-secondary;

    .empty-icon {
      font-size: 64px;
      margin-bottom: $spacing-base;
      color: $border-light;
    }

    .empty-text {
      font-size: 16px;
      margin-bottom: $spacing-small;
    }

    .empty-hint {
      font-size: 14px;
      opacity: 0.8;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .batch-processor {
    .batch-info-card {
      .batch-stats {
        flex-direction: column;
      }
    }

    .batch-item {
      .batch-item-header {
        flex-direction: column;
        align-items: stretch;
        gap: $spacing-small;
      }

      .batch-item-info {
        flex-direction: column;
        align-items: stretch;
        gap: $spacing-small;
      }
    }
  }
}
</style>