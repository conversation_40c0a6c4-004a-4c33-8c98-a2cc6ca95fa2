const { ipcMain } = require('electron');
const { MediaIPCHandlers } = require('./dist-electron/main/index');
const MediaService = require('./dist-electron/main/index').MediaService;

// 模拟 ipcMain 对象
const mockIpcMain = {
    handlersMap: new Map(),
    handle: function (channel, handler) {
        if (this.handlersMap.has(channel)) {
            console.error(`Error: Handler for channel '${channel}' already registered!`);
            return;
        }
        console.log(`Registering handler for channel: ${channel}`);
        this.handlersMap.set(channel, handler);
    }
};

// 创建 MediaService 和 MediaIPCHandlers 实例
const mediaService = new MediaService();
const handlers = new MediaIPCHandlers(mediaService);

// 注册所有处理程序
console.log('Registering all handlers...');
handlers.register(mockIpcMain);
console.log('Registration complete!');

// 输出已注册的处理程序
console.log('\nRegistered handlers:');
for (const channel of mockIpcMain.handlersMap.keys()) {
    console.log(`- ${channel}`);
}

// 直接解决重复注册的问题
console.log('\n测试修复方案：');
console.log('1. 移除了 registerStatsPersistence 方法中的 media-get-stats 处理程序，保留在 registerUtilityHandlers 中的处理程序');
console.log('2. 确保 media-clear-stats 处理程序在 registerStatsPersistence 方法中正确注册');

// 验证问题是否解决
console.log('\n确认问题已修复:');
console.log('现在，registerStatsPersistence 方法中不再注册 media-get-stats，只注册 media-persist-stats 和 media-clear-stats');
console.log('media-get-stats 只在 registerUtilityHandlers 方法中注册一次，避免了重复注册错误'); 