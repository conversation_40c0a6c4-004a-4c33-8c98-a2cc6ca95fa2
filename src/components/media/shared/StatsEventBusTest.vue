<template>
  <div class="stats-event-bus-test">
    <h3>统计事件总线测试</h3>
    
    <div class="test-controls">
      <el-button @click="testTaskCompleted" type="success">
        模拟任务完成
      </el-button>
      <el-button @click="testTaskFailed" type="danger">
        模拟任务失败
      </el-button>
      <el-button @click="testStatsUpdate" type="primary">
        模拟统计更新
      </el-button>
    </div>
    
    <div class="event-log">
      <h4>事件日志:</h4>
      <div class="log-entries">
        <div 
          v-for="(entry, index) in eventLog" 
          :key="index"
          :class="['log-entry', `log-${entry.type}`]"
        >
          <span class="timestamp">{{ entry.timestamp }}</span>
          <span class="event-type">{{ entry.eventType }}</span>
          <span class="message">{{ entry.message }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { ElButton } from 'element-plus'
import { statsEventBus, emitTaskCompleted, emitTaskFailed, emitStatsUpdate } from '@/utils/stats-event-bus'

interface LogEntry {
  timestamp: string
  eventType: string
  message: string
  type: 'success' | 'error' | 'info'
}

const eventLog = ref<LogEntry[]>([])

const addLogEntry = (eventType: string, message: string, type: LogEntry['type'] = 'info') => {
  const timestamp = new Date().toLocaleTimeString()
  eventLog.value.unshift({
    timestamp,
    eventType,
    message,
    type
  })
  
  // 限制日志条数
  if (eventLog.value.length > 50) {
    eventLog.value = eventLog.value.slice(0, 50)
  }
}

// 事件处理器
const handleStatsUpdate = () => {
  addLogEntry('stats-update', '收到统计更新事件', 'info')
}

const handleTaskCompleted = (data: { taskId: string, taskType: string }) => {
  addLogEntry('task-completed', `任务完成: ${data.taskId} (${data.taskType})`, 'success')
}

const handleTaskFailed = (data: { taskId: string, taskType: string }) => {
  addLogEntry('task-failed', `任务失败: ${data.taskId} (${data.taskType})`, 'error')
}

const handleTaskStarted = (data: { taskId: string, taskType: string }) => {
  addLogEntry('task-started', `任务开始: ${data.taskId} (${data.taskType})`, 'info')
}

// 测试方法
const testTaskCompleted = () => {
  const taskId = `test_task_${Date.now()}`
  const taskType = 'video-convert'
  emitTaskCompleted(taskId, taskType)
  addLogEntry('TEST', `发送任务完成事件: ${taskId}`, 'success')
}

const testTaskFailed = () => {
  const taskId = `test_task_${Date.now()}`
  const taskType = 'audio-extract'
  emitTaskFailed(taskId, taskType)
  addLogEntry('TEST', `发送任务失败事件: ${taskId}`, 'error')
}

const testStatsUpdate = () => {
  emitStatsUpdate()
  addLogEntry('TEST', '发送统计更新事件', 'info')
}

// 生命周期
onMounted(() => {
  // 注册事件监听器
  statsEventBus.on('stats-update', handleStatsUpdate)
  statsEventBus.on('task-completed', handleTaskCompleted)
  statsEventBus.on('task-failed', handleTaskFailed)
  statsEventBus.on('task-started', handleTaskStarted)
  
  addLogEntry('SYSTEM', '事件监听器已注册', 'info')
})

onUnmounted(() => {
  // 清理事件监听器
  statsEventBus.off('stats-update', handleStatsUpdate)
  statsEventBus.off('task-completed', handleTaskCompleted)
  statsEventBus.off('task-failed', handleTaskFailed)
  statsEventBus.off('task-started', handleTaskStarted)
  
  addLogEntry('SYSTEM', '事件监听器已清理', 'info')
})
</script>

<style scoped lang="scss">
.stats-event-bus-test {
  padding: 20px;
  
  .test-controls {
    margin-bottom: 20px;
    display: flex;
    gap: 10px;
  }
  
  .event-log {
    .log-entries {
      max-height: 400px;
      overflow-y: auto;
      border: 1px solid #ddd;
      border-radius: 4px;
      padding: 10px;
      background: #f9f9f9;
      
      .log-entry {
        display: flex;
        gap: 10px;
        padding: 5px 0;
        border-bottom: 1px solid #eee;
        font-family: monospace;
        font-size: 12px;
        
        &:last-child {
          border-bottom: none;
        }
        
        .timestamp {
          color: #666;
          min-width: 80px;
        }
        
        .event-type {
          font-weight: bold;
          min-width: 120px;
        }
        
        .message {
          flex: 1;
        }
        
        &.log-success {
          .event-type {
            color: #67c23a;
          }
        }
        
        &.log-error {
          .event-type {
            color: #f56c6c;
          }
        }
        
        &.log-info {
          .event-type {
            color: #409eff;
          }
        }
      }
    }
  }
}
</style>
