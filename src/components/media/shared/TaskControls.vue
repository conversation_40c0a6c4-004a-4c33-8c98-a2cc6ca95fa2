<template>
  <div class="task-controls">
    <div class="control-group primary-controls">
      <el-button 
        type="primary" 
        size="large"
        :loading="isProcessing"
        @click="handleStart"
        :disabled="!canStart"
      >
        <Icon icon="mdi:play" v-if="!isProcessing" />
        <Icon icon="mdi:dots-horizontal" v-if="isProcessing" />
        {{ getStartButtonText() }}
      </el-button>

      <el-button 
        size="large"
        :disabled="!canPause"
        @click="handlePause"
        v-if="showPause"
      >
        <Icon icon="mdi:pause" />
        暂停
      </el-button>

      <el-button 
        size="large"
        :disabled="!canResume"
        @click="handleResume"
        v-if="showResume"
      >
        <Icon icon="mdi:play" />
        继续
      </el-button>

      <el-button 
        size="large"
        type="danger"
        :disabled="!canStop"
        @click="handleStop"
        v-if="showStop"
      >
        <Icon icon="mdi:stop" />
        停止
      </el-button>
    </div>

    <!-- 批量操作控制 -->
    <div class="control-group batch-controls" v-if="mode === 'batch' && showBatchControls">
      <el-divider direction="vertical" />
      
      <el-button 
        @click="handleStartAll"
        :disabled="!canStartAll"
        v-if="showStartAll"
      >
        <Icon icon="mdi:play-box-multiple" />
        全部开始
      </el-button>

      <el-button 
        @click="handlePauseAll"
        :disabled="!canPauseAll"
        v-if="showPauseAll"
      >
        <Icon icon="mdi:pause-box-multiple" />
        暂停全部
      </el-button>

      <el-button 
        @click="handleRetryFailed"
        :disabled="!canRetryFailed"
        v-if="showRetryFailed && failedTasksCount > 0"
      >
        <Icon icon="mdi:refresh-circle" />
        重试失败 ({{ failedTasksCount }})
      </el-button>

      <el-button 
        type="danger"
        @click="handleClearAll"
        :disabled="!canClearAll"
        v-if="showClearAll"
      >
        <Icon icon="mdi:trash-can-outline" />
        清空队列
      </el-button>
    </div>

    <!-- 高级控制 -->
    <div class="control-group advanced-controls" v-if="showAdvanced">
      <el-divider direction="vertical" />
      
      <el-dropdown trigger="click" v-if="showPriorityControl">
        <el-button size="small">
          <Icon icon="mdi:speedometer" />
          优先级
          <Icon icon="mdi:chevron-down" />
        </el-button>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item @click="setPriority('high')">
              <Icon icon="mdi:priority-high" />
              高优先级
            </el-dropdown-item>
            <el-dropdown-item @click="setPriority('normal')">
              <Icon icon="mdi:priority-normal" />
              普通优先级
            </el-dropdown-item>
            <el-dropdown-item @click="setPriority('low')">
              <Icon icon="mdi:priority-low" />
              低优先级
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>

      <el-dropdown trigger="click" v-if="showConcurrencyControl">
        <el-button size="small">
          <Icon icon="mdi:cpu-64-bit" />
          并发数: {{ currentConcurrency }}
          <Icon icon="mdi:chevron-down" />
        </el-button>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item 
              v-for="num in concurrencyOptions" 
              :key="num"
              @click="setConcurrency(num)"
              :class="{ 'is-active': currentConcurrency === num }"
            >
              {{ num }} 个并发
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>

      <el-button 
        size="small"
        @click="handleExportTasks"
        :disabled="!canExport"
        v-if="showExport"
      >
        <Icon icon="mdi:export" />
        导出任务
      </el-button>
    </div>

    <!-- 状态指示器 -->
    <div class="status-indicator" v-if="showStatus">
      <el-divider direction="vertical" />
      
      <div class="status-item">
        <Icon :icon="getStatusIcon()" :class="getStatusClass()" />
        <span class="status-text">{{ getStatusText() }}</span>
      </div>

      <div class="progress-info" v-if="overallProgress !== undefined">
        <el-progress 
          :percentage="overallProgress" 
          :stroke-width="4"
          :show-text="false"
          :status="getProgressStatus()"
        />
        <span class="progress-text">{{ overallProgress }}%</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Icon } from '@iconify/vue'

// Props
interface Props {
  mode?: 'single' | 'batch'
  status: 'idle' | 'processing' | 'paused' | 'completed' | 'error'
  isProcessing?: boolean
  
  // 按钮显示控制
  showPause?: boolean
  showResume?: boolean
  showStop?: boolean
  showBatchControls?: boolean
  showStartAll?: boolean
  showPauseAll?: boolean
  showRetryFailed?: boolean
  showClearAll?: boolean
  showAdvanced?: boolean
  showPriorityControl?: boolean
  showConcurrencyControl?: boolean
  showExport?: boolean
  showStatus?: boolean
  
  // 状态信息
  totalTasks?: number
  completedTasks?: number
  failedTasksCount?: number
  overallProgress?: number
  
  // 能力控制
  canStart?: boolean
  canPause?: boolean
  canResume?: boolean
  canStop?: boolean
  canStartAll?: boolean
  canPauseAll?: boolean
  canRetryFailed?: boolean
  canClearAll?: boolean
  canExport?: boolean
  
  // 高级设置
  currentConcurrency?: number
  maxConcurrency?: number
  startButtonText?: string
  processingButtonText?: string
}

const props = withDefaults(defineProps<Props>(), {
  mode: 'single',
  status: 'idle',
  isProcessing: false,
  
  showPause: true,
  showResume: true,
  showStop: true,
  showBatchControls: true,
  showStartAll: true,
  showPauseAll: true,
  showRetryFailed: true,
  showClearAll: true,
  showAdvanced: false,
  showPriorityControl: false,
  showConcurrencyControl: false,
  showExport: false,
  showStatus: true,
  
  totalTasks: 0,
  completedTasks: 0,
  failedTasksCount: 0,
  overallProgress: undefined,
  
  canStart: true,
  canPause: false,
  canResume: false,
  canStop: false,
  canStartAll: false,
  canPauseAll: false,
  canRetryFailed: false,
  canClearAll: true,
  canExport: false,
  
  currentConcurrency: 2,
  maxConcurrency: 8,
  startButtonText: '',
  processingButtonText: ''
})

// Emits
const emit = defineEmits<{
  start: []
  pause: []
  resume: []
  stop: []
  startAll: []
  pauseAll: []
  retryFailed: []
  clearAll: []
  setPriority: [priority: 'high' | 'normal' | 'low']
  setConcurrency: [concurrency: number]
  exportTasks: []
}>()

// 计算属性
const concurrencyOptions = computed(() => {
  const options: number[] = []
  for (let i = 1; i <= props.maxConcurrency; i++) {
    options.push(i)
  }
  return options
})

// 事件处理
const handleStart = () => {
  emit('start')
}

const handlePause = () => {
  emit('pause')
}

const handleResume = () => {
  emit('resume')
}

const handleStop = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要停止当前任务吗？正在处理的任务将被取消。',
      '确认停止',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    emit('stop')
  } catch {
    // 用户取消
  }
}

const handleStartAll = () => {
  emit('startAll')
}

const handlePauseAll = () => {
  emit('pauseAll')
}

const handleRetryFailed = () => {
  emit('retryFailed')
}

const handleClearAll = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要清空所有任务吗？这将删除所有待处理和已完成的任务。',
      '确认清空',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    emit('clearAll')
  } catch {
    // 用户取消
  }
}

const setPriority = (priority: 'high' | 'normal' | 'low') => {
  emit('setPriority', priority)
  ElMessage.success(`优先级已设置为${priority === 'high' ? '高' : priority === 'normal' ? '普通' : '低'}`)
}

const setConcurrency = (concurrency: number) => {
  emit('setConcurrency', concurrency)
  ElMessage.success(`并发数已设置为 ${concurrency}`)
}

const handleExportTasks = () => {
  emit('exportTasks')
}

// 工具方法
const getStartButtonText = (): string => {
  if (props.isProcessing) {
    return props.processingButtonText || 
           (props.mode === 'batch' ? '批量处理中...' : '处理中...')
  }
  
  if (props.startButtonText) {
    return props.startButtonText
  }
  
  switch (props.mode) {
    case 'batch':
      return '开始批量处理'
    default:
      return '开始处理'
  }
}

const getStatusIcon = (): string => {
  const icons = {
    idle: 'mdi:pause-circle-outline',
    processing: 'mdi:play-circle',
    paused: 'mdi:pause-circle',
    completed: 'mdi:check-circle',
    error: 'mdi:alert-circle'
  }
  return icons[props.status] || 'mdi:help-circle'
}

const getStatusClass = (): string => {
  const classes = {
    idle: 'status-idle',
    processing: 'status-processing',
    paused: 'status-paused', 
    completed: 'status-completed',
    error: 'status-error'
  }
  return classes[props.status] || ''
}

const getStatusText = (): string => {
  const texts = {
    idle: '空闲',
    processing: '处理中',
    paused: '已暂停',
    completed: '已完成',
    error: '错误'
  }
  
  let statusText = texts[props.status] || props.status
  
  if (props.mode === 'batch' && props.totalTasks > 0) {
    statusText += ` (${props.completedTasks}/${props.totalTasks})`
  }
  
  return statusText
}

const getProgressStatus = () => {
  if (props.status === 'error') return 'exception'
  if (props.status === 'completed') return 'success'
  return undefined
}
</script>

<style scoped lang="scss">
@use '@/assets/styles/index.scss' as *;

.task-controls {
  display: flex;
  align-items: center;
  gap: $spacing-base;
  padding: $spacing-base;
  background: $background-light;
  border-radius: $border-radius-base;
  border: 1px solid $border-lighter;

  .control-group {
    display: flex;
    align-items: center;
    gap: $spacing-small;

    &.primary-controls {
      .el-button--large {
        padding: 12px 24px;
        font-weight: 500;
      }
    }

    &.batch-controls {
      .el-button {
        font-size: 12px;
        padding: 6px 12px;
      }
    }

    &.advanced-controls {
      .el-button {
        font-size: 12px;
        padding: 4px 8px;
      }
    }
  }

  .status-indicator {
    display: flex;
    align-items: center;
    gap: $spacing-small;
    margin-left: auto;

    .status-item {
      display: flex;
      align-items: center;
      gap: $spacing-small;
      font-size: 12px;

      .status-text {
        color: $text-secondary;
        font-weight: 500;
      }

      .status-idle { color: $info-color; }
      .status-processing { 
        color: $primary-color; 
        animation: pulse 2s infinite;
      }
      .status-paused { color: $warning-color; }
      .status-completed { color: $success-color; }
      .status-error { color: $danger-color; }
    }

    .progress-info {
      display: flex;
      align-items: center;
      gap: $spacing-small;
      min-width: 80px;

      .progress-text {
        font-size: 12px;
        color: $text-secondary;
        min-width: 30px;
        text-align: right;
      }
    }
  }

  :deep(.el-dropdown-menu__item) {
    &.is-active {
      background-color: $primary-color;
      color: white;
    }
  }
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.6; }
}

@keyframes loading {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

// 响应式设计
@media (max-width: 768px) {
  .task-controls {
    flex-direction: column;
    align-items: stretch;
    gap: $spacing-small;

    .control-group {
      justify-content: center;

      &.advanced-controls,
      &.batch-controls {
        order: 2;
      }
    }

    .status-indicator {
      margin-left: 0;
      justify-content: center;
      order: 1;
    }
  }
}
</style>