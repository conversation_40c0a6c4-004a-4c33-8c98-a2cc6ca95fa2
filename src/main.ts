import { createApp } from 'vue'
import { createPinia } from 'pinia' // Import Pinia
import ElementPlus from 'element-plus' // Import Element Plus
import 'element-plus/dist/index.css' // Import Element Plus styles
import './style.css' // Keep existing styles
import '@/assets/styles/global.scss'; // Import global styles
import App from './App.vue'
import router from './router' // Import router
import type { IpcRendererEvent } from 'electron' // Import Electron event type

// Create Pinia instance
const pinia = createPinia()
const app = createApp(App)

// Use plugins
app.use(pinia)
app.use(ElementPlus)
app.use(router) // Use router

// Mount the app
app.mount('#app')

// Optional: Log message from main process (example from preload script)
// Use the types defined in vite-env.d.ts
window.ipcRenderer?.on('main-process-message', (_event: IpcRendererEvent, message: string) => { // Added types for _event and message
    console.log('Message from main process:', message)
})
