<template>
  <BaseMediaProcessor
    title="音视频转换"
    icon="mdi:video-outline"
    component-class="video-converter"
    task-type="video-convert"
    :max-file-size="1024"
    upload-text="拖拽视频文件到此处或点击上传"
    upload-hint="支持 MP4, AVI, MOV, MKV, FLV, WMV 格式，最大 1GB"
    :default-options="defaultVideoOptions"
    options-title="转换设置"
    :show-presets="true"
    :show-save-preset="false"
    @file-uploaded="handleFileUploaded"
    @task-started="handleTaskStarted"
    @error="handleError"
  >
    <!-- 自定义转换选项 -->
    <template #options="{ modelValue }">
      <div class="video-options">
        <ProcessingOptions
          v-model="modelValue"
          task-type="video-convert"
          :show-presets="true"
        />
        
        <!-- 高级选项 -->
        <el-collapse v-model="expandedPanels" class="advanced-options">
          <el-collapse-item name="advanced" title="高级选项">
            <div class="option-row">
              <label>编码器:</label>
              <el-select v-model="modelValue.encoder" placeholder="选择编码器">
                <el-option label="H.264 (兼容性好)" value="libx264" />
                <el-option label="H.265 (更小体积)" value="libx265" />
                <el-option label="VP9" value="libvpx-vp9" />
              </el-select>
            </div>
            
            <div class="option-row">
              <label>比特率控制:</label>
              <el-radio-group v-model="modelValue.bitrateMode">
                <el-radio value="auto">自动</el-radio>
                <el-radio value="cbr">固定比特率</el-radio>
                <el-radio value="vbr">可变比特率</el-radio>
              </el-radio-group>
            </div>
            
            <div class="option-row" v-if="modelValue.bitrateMode !== 'auto'">
              <label>目标比特率:</label>
              <el-input-number
                v-model="modelValue.bitrate"
                :min="100"
                :max="50000"
                :step="100"
                :controls="false"
              />
              <span class="unit">kbps</span>
            </div>
          </el-collapse-item>
        </el-collapse>
      </div>
    </template>

    <!-- 自定义任务控制 -->
    <template #task-controls="{ onStart, onCreateBatch, canStart, showBatchCreate }">
      <div class="video-controls">
        <el-button 
          type="primary" 
          size="large"
          :disabled="!canStart"
          @click="onStart"
        >
          <Icon icon="mdi:play-circle" />
          开始转换
        </el-button>
        
        <el-button 
          v-if="showBatchCreate"
          type="success"
          @click="onCreateBatch"
        >
          <Icon icon="mdi:playlist-plus" />
          批量转换
        </el-button>
        
        <el-button 
          type="info"
          @click="showPreview"
          :disabled="!canStart"
        >
          <Icon icon="mdi:eye" />
          预览设置
        </el-button>
      </div>
    </template>

    <!-- 预览对话框 -->
    <template #footer>
      <el-dialog
        v-model="previewVisible"
        title="转换预览"
        width="600px"
        :before-close="closePreview"
      >
        <div class="preview-content" v-if="previewData">
          <div class="preview-info">
            <h4>输入文件信息</h4>
            <el-descriptions :column="2" border>
              <el-descriptions-item label="文件名">{{ previewData.fileName }}</el-descriptions-item>
              <el-descriptions-item label="文件大小">{{ formatFileSize(previewData.fileSize) }}</el-descriptions-item>
              <el-descriptions-item label="格式">{{ previewData.inputFormat }}</el-descriptions-item>
              <el-descriptions-item label="预计时长">{{ previewData.duration || '未知' }}</el-descriptions-item>
            </el-descriptions>
          </div>
          
          <div class="preview-settings">
            <h4>转换设置</h4>
            <el-descriptions :column="2" border>
              <el-descriptions-item label="输出格式">{{ previewData.outputFormat }}</el-descriptions-item>
              <el-descriptions-item label="质量">{{ previewData.quality }}</el-descriptions-item>
              <el-descriptions-item label="分辨率">{{ previewData.resolution }}</el-descriptions-item>
              <el-descriptions-item label="编码器">{{ previewData.encoder || '自动' }}</el-descriptions-item>
            </el-descriptions>
          </div>
          
          <div class="preview-estimate">
            <h4>预计结果</h4>
            <el-alert
              :title="`预计输出文件大小: ${previewData.estimatedSize || '未知'}`"
              type="info"
              :closable="false"
            />
          </div>
        </div>
        
        <template #footer>
          <el-button @click="closePreview">关闭</el-button>
          <el-button type="primary" @click="startFromPreview">开始转换</el-button>
        </template>
      </el-dialog>
    </template>
  </BaseMediaProcessor>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { Icon } from '@iconify/vue'
import BaseMediaProcessor from './shared/BaseMediaProcessor.vue'
import ProcessingOptions from './shared/ProcessingOptions.vue'

// 默认视频转换选项
const defaultVideoOptions = {
  outputFormat: 'mp4',
  quality: 'medium',
  resizeEnabled: false,
  maxWidth: 1920,
  encoder: 'libx264',
  bitrateMode: 'auto',
  bitrate: 2000
}

// 组件状态
const expandedPanels = ref([''])
const previewVisible = ref(false)
const previewData = ref<any>(null)

// 获取基础组件的引用
const baseProcessor = ref<InstanceType<typeof BaseMediaProcessor>>()

// 事件处理
const handleFileUploaded = (payload: { name: string; size?: number; path: string }) => {
  console.log('[VideoConverter] 文件已上传:', payload)
  ElMessage.success(`视频文件 "${payload.name}" 已添加`)
}

const handleTaskStarted = (taskId: string) => {
  console.log('[VideoConverter] 任务已开始:', taskId)
  ElMessage.success('视频转换任务已开始')
}

const handleError = (message: string) => {
  console.error('[VideoConverter] 错误:', message)
}

// 预览功能
const showPreview = () => {
  if (!baseProcessor.value?.fileList.length) {
    ElMessage.warning('请先添加视频文件')
    return
  }

  const firstFile = baseProcessor.value.fileList[0]
  const options = baseProcessor.value.processingOptions

  previewData.value = {
    fileName: firstFile.name,
    fileSize: firstFile.size || 0,
    inputFormat: getFileExtension(firstFile.name),
    duration: '待分析',
    outputFormat: options.outputFormat || 'mp4',
    quality: getQualityLabel(options.quality),
    resolution: getResolutionLabel(options),
    encoder: getEncoderLabel(options.encoder),
    estimatedSize: '计算中...'
  }

  previewVisible.value = true
}

const closePreview = () => {
  previewVisible.value = false
  previewData.value = null
}

const startFromPreview = () => {
  closePreview()
  baseProcessor.value?.startSingleTask()
}

// 工具函数
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const getFileExtension = (filename: string): string => {
  return filename.split('.').pop()?.toUpperCase() || '未知'
}

const getQualityLabel = (quality: string): string => {
  const labels = {
    'low': '低质量 (快速)',
    'medium': '中等质量 (平衡)',
    'high': '高质量 (慢速)',
    'ultra': '超高质量 (最慢)'
  }
  return labels[quality as keyof typeof labels] || quality
}

const getResolutionLabel = (options: any): string => {
  if (!options.resizeEnabled) return '保持原分辨率'
  return `最大宽度: ${options.maxWidth}px`
}

const getEncoderLabel = (encoder: string): string => {
  const labels = {
    'libx264': 'H.264',
    'libx265': 'H.265/HEVC',
    'libvpx-vp9': 'VP9'
  }
  return labels[encoder as keyof typeof labels] || encoder
}

// 暴露给父组件
defineExpose({
  startConversion: () => baseProcessor.value?.startSingleTask(),
  createBatch: () => baseProcessor.value?.createBatchTask(),
  clearAll: () => baseProcessor.value?.clearAll()
})
</script>

<style lang="scss" scoped>
@import './shared/media-common';

.video-options {
  .advanced-options {
    margin-top: $spacing-base;
    
    .option-row {
      display: flex;
      align-items: center;
      gap: $spacing-base;
      margin-bottom: $spacing-sm;

      label {
        min-width: 100px;
        color: $text-secondary;
        font-size: 13px;
      }

      .unit {
        margin-left: $spacing-xs;
        color: $text-secondary;
        font-size: 12px;
      }
    }
  }
}

.video-controls {
  display: flex;
  gap: $spacing-base;
  justify-content: center;
  flex-wrap: wrap;
}

.preview-content {
  .preview-info,
  .preview-settings,
  .preview-estimate {
    margin-bottom: $spacing-base;

    h4 {
      margin-bottom: $spacing-sm;
      color: $text-primary;
      font-size: 14px;
    }
  }
}

// 应用视频转换主题
@include task-type-theme($color-video);
</style>