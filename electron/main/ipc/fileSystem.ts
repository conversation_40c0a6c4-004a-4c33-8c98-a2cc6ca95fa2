import { ipcMain } from 'electron';
import * as fs from 'fs';
import * as path from 'path';

export function registerFileSystemHandlers(
    ipcMain: Electron.IpcMain,
    fs: typeof import('fs'),
    path: typeof import('path')
): void {
    ipcMain.handle("path-dirname", async (event, filePath: string) => {
        console.log(`[IPC] 获取目录名: ${filePath}`);
        return path.dirname(filePath);
    });

    ipcMain.handle("path-basename", async (event, filePath: string) => {
        console.log(`[IPC] 获取文件名: ${filePath}`);
        return path.basename(filePath);
    });

    ipcMain.handle("path-sep", async () => {
        console.log(`[IPC] 获取路径分隔符`);
        return path.sep;
    });

    ipcMain.handle("fs-mkdirSync", async (event, dirPath: string, options?: fs.MakeDirectoryOptions) => {
        console.log(`[IPC] 创建目录: ${dirPath}`);
        fs.mkdirSync(dirPath, options);
    });

    ipcMain.handle("fs-existsSync", async (event, filePath: string) => {
        console.log(`[IPC] 检查文件/目录是否存在: ${filePath}`);
        return fs.existsSync(filePath);
    });
}